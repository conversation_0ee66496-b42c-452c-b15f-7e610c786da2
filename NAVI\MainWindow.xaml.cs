﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using NAVI.Services.DAL;

namespace NAVI
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private bool _isLoggedIn = false;
        private string _currentUserId = "";
        private User _currentUser = null;

        public MainWindow()
        {
            InitializeComponent();
            ShowLoginInterface();
        }

        /// <summary>
        /// 显示登录界面
        /// </summary>
        private void ShowLoginInterface()
        {
            _isLoggedIn = false;
            LoginLayout.Visibility = Visibility.Visible;
            MainLayout.Visibility = Visibility.Collapsed;
            MainContent.Content = new LoginControl(this);
        }

        /// <summary>
        /// 登录成功后导航到主界面
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="user">用户信息</param>
        public void NavigateToMain(string userId, User user = null)
        {
            _isLoggedIn = true;
            _currentUserId = userId;
            _currentUser = user;

            // 切换到登录后的界面布局
            LoginLayout.Visibility = Visibility.Collapsed;
            MainLayout.Visibility = Visibility.Visible;

            // 更新用户信息显示
            if (user != null)
            {
                UserInfoText.Text = $"{user.氏名} ({user.役職}) - ID: {userId}";
            }
            else
            {
                UserInfoText.Text = $"ID: {userId}";
            }

            // 显示主控制界面
            LoggedInContent.Content = new MainControl();
        }

        /// <summary>
        /// 获取当前登录用户信息
        /// </summary>
        public User GetCurrentUser()
        {
            return _currentUser;
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        public string GetCurrentUserId()
        {
            return _currentUserId;
        }

        /// <summary>
        /// 退出登录按钮点击事件
        /// </summary>
        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要退出登录吗？", "确认退出",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ShowLoginInterface();
            }
        }

        /// <summary>
        /// 更新状态栏信息
        /// </summary>
        /// <param name="recordCount">记录数</param>
        /// <param name="selectedCount">选中数</param>
        /// <param name="pageCount">页数</param>
        public void UpdateStatusBar(int recordCount, int selectedCount, int pageCount,string message)
        {
            if (_isLoggedIn)
            {
                RecordCountText.Text = $"记录数: {recordCount}件";
                SelectedCountText.Text = $"已选择: {selectedCount}件";
                PageCountText.Text = $"页数: {pageCount}件";
            }
        }

        /// <summary>
        /// 打开MonthYearPicker测试窗口
        /// </summary>
        public void OpenMonthYearPickerTest()
        {
            var testWindow = new TestMonthYearPickerWindow();
            testWindow.Owner = this;
            testWindow.ShowDialog();
        }
    }
}
