﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Microsoft.Win32;
using System.IO;

namespace NAVI
{
    /// <summary>
    /// SubsidyCsvExportControl.xaml 的交互逻辑
    /// </summary>
    public partial class SubsidyCsvExportControl : UserControl
    {
        public SubsidyCsvExportControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 隐藏成功消息面板
            SuccessMessagePanel.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 出力实行按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取选择的开始和结束月份
                var startMonth = (StartMonthComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();
                var endMonth = (EndMonthComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();
                
                if (string.IsNullOrEmpty(startMonth) || string.IsNullOrEmpty(endMonth))
                {
                    MessageBox.Show("開始月と終了月を選択してください。", "入力エラー", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 验证日期范围
                if (!ValidateDateRange(startMonth, endMonth))
                {
                    MessageBox.Show("終了月は開始月以降を選択してください。", "入力エラー", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 显示文件保存对话框
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "補助金CSVファイルを保存",
                    Filter = "CSVファイル (*.csv)|*.csv|すべてのファイル (*.*)|*.*",
                    FilterIndex = 1,
                    RestoreDirectory = true,
                    FileName = $"補助金データ_{startMonth}_{endMonth}_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // 执行CSV导出
                    ExportSubsidyCsv(saveFileDialog.FileName, startMonth, endMonth);
                    
                    // 显示成功消息
                    ShowSuccessMessage($"補助金CSVファイルが正常に出力されました。\n保存先: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"補助金CSV出力でエラーが発生しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // 隐藏成功消息面板
            SuccessMessagePanel.Visibility = Visibility.Collapsed;
            
            // 重置选择
            StartMonthComboBox.SelectedIndex = 0;
            EndMonthComboBox.SelectedIndex = 0;
        }

        /// <summary>
        /// 验证日期范围
        /// </summary>
        /// <param name="startMonth">开始月份</param>
        /// <param name="endMonth">结束月份</param>
        /// <returns>是否有效</returns>
        private bool ValidateDateRange(string startMonth, string endMonth)
        {
            try
            {
                // 简单的日期范围验证
                // 这里可以根据实际需求实现更复杂的验证逻辑
                var startIndex = StartMonthComboBox.SelectedIndex;
                var endIndex = EndMonthComboBox.SelectedIndex;
                
                // 由于ComboBox中的项目是按时间倒序排列的，所以startIndex应该大于等于endIndex
                return startIndex >= endIndex;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 执行补助金CSV导出
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="startMonth">开始月份</param>
        /// <param name="endMonth">结束月份</param>
        private void ExportSubsidyCsv(string filePath, string startMonth, string endMonth)
        {
            try
            {
                // 创建CSV内容
                var csvContent = new StringBuilder();
                
                // 添加CSV头部
                csvContent.AppendLine("No,登録日,事業者番号,事業者名称,サービス提供年月,補助金種別,補助金額,受給者番号,サービスコード,サービス内容,算定単価額,利用日数,補助対象額");
                
                // TODO: 从数据库获取实际的补助金数据
                // 这里添加示例数据，实际应用中应该从数据库查询指定期间的数据
                int recordCount = 1;
                var currentMonth = startMonth;
                
                // 模拟生成期间内的数据
                for (int month = 0; month < 4; month++) // 假设生成4个月的数据
                {
                    for (int i = 1; i <= 5; i++) // 每月5条记录
                    {
                        csvContent.AppendLine($"{recordCount},2025/01/15,1234567890,サンプル事業者{i},{currentMonth},地域生活支援補助金,25000,SUB{recordCount:D6},241111,福祉短期入所Ⅰ６,8500,15,127500");
                        recordCount++;
                    }
                }
                
                // 写入文件
                File.WriteAllText(filePath, csvContent.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"CSV出力処理に失敗しました: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示成功消息
        /// </summary>
        /// <param name="message">消息内容</param>
        private void ShowSuccessMessage(string message)
        {
            ExportResultText.Text = message;
            SuccessMessagePanel.Visibility = Visibility.Visible;
        }
    }
}
