﻿<Window x:Class="NAVI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:NAVI"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="都加算NAVI【仮称】- 地方自治体福祉業務管理システム v1.105"
        Height="800" Width="1200"
        Icon="upload.png"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <!-- 定义颜色资源 -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2986A8"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF03DAC6"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#FFF5F5F5"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFFFF"/>
        <SolidColorBrush x:Key="OnSurfaceBrush" Color="#FF212121"/>

        <!-- 标题栏样式 -->
        <Style x:Key="TitleBarStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Height" Value="60"/>
        </Style>

        <!-- 状态栏样式 -->
        <Style x:Key="StatusBarStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF37474F"/>
            <Setter Property="Height" Value="24"/>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- 登录前的简洁界面 -->
        <Grid x:Name="LoginLayout" Visibility="Visible">
            <ContentControl x:Name="MainContent" />
        </Grid>

        <!-- 登录后的完整界面 -->
        <Grid x:Name="MainLayout" Visibility="Collapsed">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>  <!-- 标题栏 -->
                <RowDefinition Height="*"/>   <!-- 主内容区 -->
                <RowDefinition Height="24"/>  <!-- 状态栏 -->
            </Grid.RowDefinitions>

            <!-- 顶部标题栏 -->
            <Border Grid.Row="0" Style="{StaticResource TitleBarStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧图标和标题 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <TextBlock Text="都加算NAVI"
                                  FontSize="20"
                                  FontWeight="Medium"
                                  Foreground="White"
                                  VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- 中间用户信息 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,16,0">
                        <TextBlock Text="用户登录区域:"
                                  Foreground="White"
                                  VerticalAlignment="Center"
                                  Margin="0,0,8,0"/>
                        <TextBlock x:Name="UserInfoText"
                                  Text="ID: 2025011741"
                                  Foreground="White"
                                  FontWeight="Medium"
                                  VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- 右侧按钮 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,16,0">
                        <Button Content="退出登录"
                               Background="Transparent"
                               Foreground="White"
                               BorderBrush="White"
                               BorderThickness="1"
                               Padding="12,6"
                               FontFamily="Microsoft YaHei UI"
                               Margin="8,0"
                               Click="LogoutButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 主内容区域 -->
            <Border Grid.Row="1" Background="{StaticResource BackgroundBrush}">
                <ContentControl x:Name="LoggedInContent" />
            </Border>

            <!-- 底部状态栏 -->
            <Border Grid.Row="2" Style="{StaticResource StatusBarStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,8,0">
                        <TextBlock x:Name="RecordCountText"
                                  Text="记录数: 4件"
                                  Foreground="White"
                                  Margin="0,0,16,0"/>
                        <TextBlock x:Name="SelectedCountText"
                                  Text="已选择: 0件"
                                  Foreground="White"
                                  Margin="0,0,16,0"/>
                        <TextBlock x:Name="PageCountText"
                                  Text="页数: 4件"
                                  Foreground="White"/>
                    </StackPanel>
                    <TextBlock Grid.Column="1"
                              Text="就绪"
                              Foreground="White"
                              VerticalAlignment="Center"
                              Margin="8,0"/>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
