<UserControl x:Class="NAVI.FinancialStatisticsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:local="clr-namespace:NAVI"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF2986A8"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                               CornerRadius="4" 
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 次要按钮样式 -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 统计卡片样式 -->
        <Style x:Key="StatisticsCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFDDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 统计标题样式 -->
        <Style x:Key="StatisticsTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#FF666666"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <!-- 统计数值样式 -->
        <Style x:Key="StatisticsValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <!-- 金额样式 -->
        <Style x:Key="AmountStyle" TargetType="TextBlock" BasedOn="{StaticResource StatisticsValueStyle}">
            <Setter Property="Foreground" Value="#FF2986A8"/>
        </Style>

        <!-- 数量样式 -->
        <Style x:Key="CountStyle" TargetType="TextBlock" BasedOn="{StaticResource StatisticsValueStyle}">
            <Setter Property="Foreground" Value="#FF4CAF50"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="#FFF5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="White" BorderBrush="#FFDDDDDD" BorderThickness="0,0,0,1" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="財務状況" FontSize="24" FontWeight="Bold" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0" >
                    <TextBlock Text="対象年度：" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>
                    <ComboBox Name="YearComboBox" Width="140" SelectedIndex="0" FontWeight="Bold" SelectionChanged="YearComboBox_SelectionChanged">
                        <ComboBoxItem Content="令和7年度 (2025年)"/>
                        <ComboBoxItem Content="令和6年度 (2024年)"/>
                        <ComboBoxItem Content="令和5年度 (2023年)"/>
                        <ComboBoxItem Content="令和4年度 (2022年)"/>
                        <ComboBoxItem Content="令和3年度 (2021年)"/>
                    </ComboBox>
                </StackPanel>

                <TextBlock Grid.Column="2" Text="戻る" VerticalAlignment="Center" FontSize="14" FontWeight="Bold" Foreground="#FF666666"/>
            </Grid>
        </Border>

        <!-- 统计卡片区域 -->
        <Border Grid.Row="1" Background="White" Padding="20">
            <StackPanel>
                <TextBlock Text="事業者別請求金額集計表" FontSize="18" FontWeight="Bold" Foreground="#FF2986A8" Margin="0,0,0,20"/>
                
                <!-- 统计卡片 -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 给付事业者数 -->
                    <Border Grid.Column="0" Style="{StaticResource StatisticsCardStyle}">
                        <StackPanel>
                            <TextBlock Text="給付事業者数" Style="{StaticResource StatisticsTitleStyle}"/>
                            <TextBlock Name="ProviderCountText" Text="4" Style="{StaticResource CountStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 当月基本给付 -->
                    <Border Grid.Column="1" Style="{StaticResource StatisticsCardStyle}">
                        <StackPanel>
                            <TextBlock Text="当月基本給付" Style="{StaticResource StatisticsTitleStyle}"/>
                            <TextBlock Name="CurrentMonthAmountText" Text="¥3,405,000" Style="{StaticResource AmountStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- 上期合计 -->
                    <Border Grid.Column="2" Style="{StaticResource StatisticsCardStyle}">
                        <StackPanel>
                            <TextBlock Text="上期合計" Style="{StaticResource StatisticsTitleStyle}"/>
                            <TextBlock Name="PreviousTotalText" Text="¥13,205,000" Style="{StaticResource AmountStyle}" Foreground="#FF4CAF50"/>
                        </StackPanel>
                    </Border>

                    <!-- 下期合计 -->
                    <Border Grid.Column="3" Style="{StaticResource StatisticsCardStyle}">
                        <StackPanel>
                            <TextBlock Text="下期合計" Style="{StaticResource StatisticsTitleStyle}"/>
                            <TextBlock Name="NextTotalText" Text="¥0" Style="{StaticResource AmountStyle}" Foreground="#FFFF9800"/>
                        </StackPanel>
                    </Border>

                    <!-- 年度总计 -->
                    <Border Grid.Column="4" Style="{StaticResource StatisticsCardStyle}">
                        <StackPanel>
                            <TextBlock Text="年度総計" Style="{StaticResource StatisticsTitleStyle}"/>
                            <TextBlock Name="YearTotalText" Text="¥13,205,000" Style="{StaticResource AmountStyle}" Foreground="#FF2986A8"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- 筛选和操作按钮区域 -->
                <Grid Margin="0,20,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 筛选区域 -->
                    <!--<StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,0,0,15">
                        --><!-- 事业者名称筛选 --><!--
                        <TextBox Name="ProviderNameFilterTextBox"
                                Width="200" Height="36"
                                Style="{StaticResource ModernTextBoxStyle}"
                                VerticalAlignment="Center"
                                Margin="0,0,10,0"
                                Text="事業者名称・事業者番号で検索"
                                GotFocus="ProviderNameFilterTextBox_GotFocus"
                                LostFocus="ProviderNameFilterTextBox_LostFocus"
                                TextChanged="ProviderNameFilterTextBox_TextChanged"/>

                        --><!-- 月份筛选 --><!--
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,10,0">
                            <TextBlock Text="月別表示：" VerticalAlignment="Center" Margin="0,0,8,0" FontSize="13"/>
                            <ComboBox Name="MonthDisplayComboBox" Width="120" Height="36" SelectedIndex="0" SelectionChanged="MonthDisplayComboBox_SelectionChanged">
                                <ComboBoxItem Content="全月表示"/>
                                <ComboBoxItem Content="上期のみ"/>
                                <ComboBoxItem Content="下期のみ"/>
                                <ComboBoxItem Content="当月のみ"/>
                            </ComboBox>
                        </StackPanel>
                    </StackPanel>-->

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Left">
                        <!-- 事业者名称筛选 -->
                        <TextBox Name="ProviderNameFilterTextBox"
                             Width="260" Height="36"
                             Style="{StaticResource ModernTextBoxStyle}"
                             VerticalAlignment="Center"
                             Margin="0,0,10,0"
                             Text="事業者名称・事業者番号で検索"
                             GotFocus="ProviderNameFilterTextBox_GotFocus"
                             LostFocus="ProviderNameFilterTextBox_LostFocus"
                             TextChanged="ProviderNameFilterTextBox_TextChanged"/>

                        <Button Content="検索" Style="{StaticResource ModernButtonStyle}" Margin="0,0,10,0" Click="SearchButton_Click"/>
                        <Button Content="CSVエクスポート" Style="{StaticResource SecondaryButtonStyle}" Margin="0,0,10,0" Click="ExportButton_Click"/>
                        <Button Content="印刷" Style="{StaticResource SecondaryButtonStyle}" Margin="0,0,10,0" Click="PrintButton_Click"/>
                        <Button Content="更新" Style="{StaticResource SecondaryButtonStyle}" Click="RefreshButton_Click"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </Border>

        <!-- 详细数据表格 -->
        <Border Grid.Row="2" Background="White" Margin="20" CornerRadius="8" BorderBrush="#FFDDDDDD" BorderThickness="1">
            <Grid>
                <!-- 数据表格 -->
                <DataGrid Grid.Row="0" Name="FinancialDataGrid"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         CanUserReorderColumns="False"
                         CanUserResizeRows="False"
                         GridLinesVisibility="All"
                         HorizontalGridLinesBrush="#FFDDDDDD"
                         VerticalGridLinesBrush="#FFDDDDDD"
                         HeadersVisibility="Column"
                         SelectionMode="Single"
                         AlternatingRowBackground="#FFF9F9F9"
                         RowBackground="White"
                         BorderThickness="1"
                         BorderBrush="#FFDDDDDD"
                         FontSize="12">

                    <!-- DataGrid Header样式 -->
                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#FFF0F0F0"/>
                            <Setter Property="Foreground" Value="Black"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="FontSize" Value="12"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="8,6"/>
                            <Setter Property="BorderBrush" Value="#FFDDDDDD"/>
                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                            <Setter Property="Height" Value="40"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.Columns>
                        <!-- 事业者番号 -->
                        <DataGridTextColumn Header="事業者番号" Binding="{Binding 事業者番号}" Width="120" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 事业者名称 -->
                        <DataGridTextColumn Header="事業者名称" Binding="{Binding 事業者名称}" Width="120" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="FontSize" Value="12"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <!-- 4月 -->
                        <DataGridTextColumn Header="4月" Binding="{Binding 四月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 5月 -->
                        <DataGridTextColumn Header="5月" Binding="{Binding 五月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 6月 -->
                        <DataGridTextColumn Header="6月" Binding="{Binding 六月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 7月 -->
                        <DataGridTextColumn Header="7月" Binding="{Binding 七月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 8月 -->
                        <DataGridTextColumn Header="8月" Binding="{Binding 八月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 9月 -->
                        <DataGridTextColumn Header="9月" Binding="{Binding 九月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 10月 -->
                        <DataGridTextColumn Header="10月" Binding="{Binding 十月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 11月 -->
                        <DataGridTextColumn Header="11月" Binding="{Binding 十一月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 12月 -->
                        <DataGridTextColumn Header="12月" Binding="{Binding 十二月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                    
                        <!-- 1月 -->
                        <DataGridTextColumn Header="1月" Binding="{Binding 一月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="10,8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 2月 -->
                        <DataGridTextColumn Header="2月" Binding="{Binding 二月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 3月 -->
                        <DataGridTextColumn Header="3月" Binding="{Binding 三月}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 上期合计 -->
                        <DataGridTextColumn Header="上期合計" Binding="{Binding 上期合計}" Width="100" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 下期合计 -->
                        <DataGridTextColumn Header="下期合計" Binding="{Binding 下期合計}" Width="100" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Background" Value="#FFFFF3E0"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 年度合计 -->
                        <DataGridTextColumn Header="年度合計" Binding="{Binding 年度合計}" Width="100" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="4,6"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="12"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Background" Value="#FFE3F2FD"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
