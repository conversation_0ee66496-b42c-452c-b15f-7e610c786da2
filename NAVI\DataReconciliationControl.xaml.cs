using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using NAVI.Services;
using NAVI.Services.DAL;

namespace NAVI
{
    /// <summary>
    /// 数据照合控件
    /// </summary>
    public partial class DataReconciliationControl : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<ReconciliationResultL> _reconciliationResults;
        private bool _isProcessing = false;
        private DatabaseManager _databaseManager;
        private RecipientRepository _recipientRepository;
        private KokuhoRenRepository _kokuhoRenRepository;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 照合结果列表
        /// </summary>
        public ObservableCollection<ReconciliationResultL> ReconciliationResults
        {
            get => _reconciliationResults;
            set
            {
                _reconciliationResults = value;
                OnPropertyChanged(nameof(ReconciliationResults));
            }
        }

        public DataReconciliationControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化数据库管理器
                _databaseManager = new DatabaseManager();
                _recipientRepository = _databaseManager.Recipients;
                _kokuhoRenRepository = _databaseManager.KokuhoRenData;

                ReconciliationResults = new ObservableCollection<ReconciliationResultL>();
                DataContext = this;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データベース初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 开始照合按钮点击事件
        /// </summary>
        private async void StartReconciliationButton_Click(object sender, RoutedEventArgs e)
        {
            if (MonthComboBox.SelectedItem == null)
            {
                MessageBox.Show("照合する月度を選択してください", "お知らせ",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedMonth = (MonthComboBox.SelectedItem as ComboBoxItem)?.Content.ToString();
            var result = MessageBox.Show($"{selectedMonth}のデータ照合を実行してもよろしいですか？", "照合確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await StartReconciliationAsync(selectedMonth);
            }
        }

        /// <summary>
        /// 开始照合处理
        /// </summary>
        private async Task StartReconciliationAsync(string selectedMonth)
        {
            try
            {
                _isProcessing = true;
                UpdateUI();

                // 显示处理状态
                EmptyState.Visibility = Visibility.Collapsed;
                ProcessingIndicator.Visibility = Visibility.Visible;
                ProcessStatusText.Text = "処理中";

                var startTime = DateTime.Now;

                // 解析选择的月份
                var serviceMonth = ParseSelectedMonth(selectedMonth);
                if (string.IsNullOrEmpty(serviceMonth))
                {
                    MessageBox.Show("月度の解析に失敗しました", "エラー",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 执行实际的照合处理
                ProcessingText.Text = "受給者データ読み込み中...";
                await Task.Delay(500);

                ProcessingText.Text = "国保連データ読み込み中...";
                await Task.Delay(500);

                ProcessingText.Text = "データ照合実行中...";
                var reconciliationResults = await ExecuteReconciliationAsync(serviceMonth);

                ProcessingText.Text = "結果集計中...";
                await Task.Delay(500);

                // 生成照合结果汇总
                GenerateReconciliationSummary(reconciliationResults);

                // 显示结果
                ProcessingIndicator.Visibility = Visibility.Collapsed;
                ReconciliationStats.Visibility = Visibility.Visible;

                var endTime = DateTime.Now;
                var processingTime = endTime - startTime;

                ProcessStatusText.Text = "完了";
                LastReconciliationText.Text = endTime.ToString("yyyy-MM-dd HH:mm");
                ProcessingTimeText.Text = $"{processingTime.TotalSeconds:F1}秒";

                ExportResultButton.IsEnabled = true;

                MessageBox.Show($"データ照合が完了しました！\n処理件数: {reconciliationResults.TotalRecords}件", "照合完了",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"照合処理中にエラーが発生しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                ProcessStatusText.Text = "エラー";
            }
            finally
            {
                _isProcessing = false;
                ProcessingIndicator.Visibility = Visibility.Collapsed;
                UpdateUI();
            }
        }

        /// <summary>
        /// 解析选择的月份
        /// </summary>
        private string ParseSelectedMonth(string selectedMonth)
        {
            try
            {
                // 从"令和7年1月度"格式中提取年月
                if (selectedMonth.Contains("令和") && selectedMonth.Contains("年") && selectedMonth.Contains("月度"))
                {
                    var yearPart = selectedMonth.Substring(selectedMonth.IndexOf("令和") + 2, selectedMonth.IndexOf("年") - 2);
                    var monthPart = selectedMonth.Substring(selectedMonth.IndexOf("年") + 1, selectedMonth.IndexOf("月度") - selectedMonth.IndexOf("年") - 1);

                    if (int.TryParse(yearPart, out int reiwaYear) && int.TryParse(monthPart, out int month))
                    {
                        // 令和年转换为西历年（令和1年=2019年）
                        int westernYear = reiwaYear + 2018;
                        return $"{westernYear:D4}-{month:D2}";
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"月份解析失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 执行照合处理
        /// </summary>
        private async Task<ReconciliationSummary> ExecuteReconciliationAsync(string serviceMonth)
        {
            try
            {
                // 1. 以受给者数据表RecipientsData为基础数据，筛选指定月份1-25号的数据
                var recipientData = await _recipientRepository.GetByServiceMonthAsync(serviceMonth);
                System.Diagnostics.Debug.WriteLine($"受给者数据筛选结果: {recipientData.Count}条");

                // 2. 查询KokuhoRenData作为参考数据表（仅供参考不做修改）
                var kokuhoRenData = await _kokuhoRenRepository.GetByServiceMonthAsync(serviceMonth);
                System.Diagnostics.Debug.WriteLine($"国保联数据筛选结果: {kokuhoRenData.Count}条");

                // 3. 创建国保联数据的查找字典（用于快速匹配）
                // 使用7个关键字段进行匹配：事業者コード、受給者番号、障害支援区分、算定時間、サービスコード、サービス名称、請求年月日
                var kokuhoRenLookup = new Dictionary<string, KokuhoRenData>();
                foreach (var kokuhoRen in kokuhoRenData)
                {
                    var lookupKey = $"{kokuhoRen.事業者コード}_{kokuhoRen.受給者番号}_{kokuhoRen.サービス提供年月}_{kokuhoRen.サービスコード}";
                    //var lookupKey = $"{kokuhoRen.事業者コード}_{kokuhoRen.受給者番号}_{kokuhoRen.障害支援区分}_{kokuhoRen.算定時間}_{kokuhoRen.サービスコード}_{kokuhoRen.サービス名称}_{kokuhoRen.請求年月日}";
                    if (!kokuhoRenLookup.ContainsKey(lookupKey))
                    {
                        kokuhoRenLookup[lookupKey] = kokuhoRen;
                    }
                }

                int matchCount = 0;
                int mismatchCount = 0;
                int nomatchCount = 0;

                // 4. 对每条受给者数据进行照合
                foreach (var recipient in recipientData)
                {
                    // 构建查找键，使用受给者数据的对应字段：事業者番号、受給者番号、障害支援区分、利用日数、サービスコード、サービス内容、開始年月日
                    //var lookupKey = $"{recipient.事業者番号}_{recipient.受給者番号}_{recipient.障害支援区分}_{recipient.利用日数}_{recipient.サービスコード}_{recipient.サービス内容}_{recipient.開始年月日}";
                    var lookupKey = $"{recipient.事業者番号}_{recipient.受給者番号}_{recipient.サービス提供年月}_{recipient.サービスコード}";

                    if (kokuhoRenLookup.TryGetValue(lookupKey, out var matchedKokuhoRen))
                    {
                        // 找到匹配记录，比较7个关键字段
                        if (CompareRecords(recipient, matchedKokuhoRen))
                        {
                            // 完全匹配
                            await _recipientRepository.UpdateStatusAsync(recipient.No, "MATCH");
                            matchCount++;
                        }
                        else
                        {
                            // 部分匹配（数据不一致）
                            await _recipientRepository.UpdateStatusAsync(recipient.No, "MISMATCH");
                            mismatchCount++;
                        }
                    }
                    else
                    {
                        // 没有找到对应数据
                        await _recipientRepository.UpdateStatusAsync(recipient.No, "NOMATCH");
                        nomatchCount++;
                    }
                }

                return new ReconciliationSummary
                {
                    TotalRecords = recipientData.Count,
                    MatchCount = matchCount,
                    MismatchCount = mismatchCount,
                    NomatchCount = nomatchCount,
                    KokuhoRenCount = kokuhoRenData.Count
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"照合处理失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 比较受给者记录和国保联记录的7个关键字段
        /// </summary>
        private bool CompareRecords(RecipientData recipient, KokuhoRenData kokuhoRen)
        {
            try
            {
                // 比较7个关键字段：
                // 1. 事業者番号 vs 事業者コード
                // 2. 受給者番号 vs 受給者番号
                // 3. 障害支援区分 vs 障害支援区分
                // 4. 利用日数 vs 算定時間
                // 5. サービスコード vs サービスコード
                // 6. サービス内容 vs サービス名称
                // 7. サービス提供年月 vs サービス提供年月

                return recipient.事業者番号?.Trim() == kokuhoRen.事業者コード?.Trim() &&
                       recipient.受給者番号?.Trim() == kokuhoRen.受給者番号?.Trim() &&
                       recipient.障害支援区分?.Trim() == kokuhoRen.障害支援区分?.Trim() &&
                       recipient.利用日数?.Trim() == kokuhoRen.算定時間?.Trim() &&
                       recipient.サービスコード?.Trim() == kokuhoRen.サービスコード?.Trim() &&
                       recipient.サービス内容?.Trim() == kokuhoRen.サービス名称?.Trim() &&
                       recipient.サービス提供年月?.Trim() == kokuhoRen.サービス提供年月?.Trim();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"记录比较失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成照合结果汇总
        /// </summary>
        private void GenerateReconciliationSummary(ReconciliationSummary summary)
        {
            ReconciliationResults.Clear();

            // 添加总体汇总结果
            ReconciliationResults.Add(new ReconciliationResultL
            {
                Category = "受給者データ総数",
                ProviderCount = summary.TotalRecords.ToString("N0"),
                NationalCount = summary.KokuhoRenCount.ToString("N0"),
                MatchedCount = summary.MatchCount.ToString("N0"),
                UnmatchedCount = (summary.MismatchCount + summary.NomatchCount).ToString("N0"),
                MatchRate = summary.TotalRecords > 0 ? $"{(double)summary.MatchCount / summary.TotalRecords * 100:F1}%" : "0.0%"
            });

            // 添加分类详细结果
            ReconciliationResults.Add(new ReconciliationResultL
            {
                Category = "完全一致 (MATCH)",
                ProviderCount = summary.MatchCount.ToString("N0"),
                NationalCount = summary.MatchCount.ToString("N0"),
                MatchedCount = summary.MatchCount.ToString("N0"),
                UnmatchedCount = "0",
                MatchRate = summary.TotalRecords > 0 ? $"{(double)summary.MatchCount / summary.TotalRecords * 100:F1}%" : "0.0%"
            });

            ReconciliationResults.Add(new ReconciliationResultL
            {
                Category = "データ不一致 (MISMATCH)",
                ProviderCount = summary.MismatchCount.ToString("N0"),
                NationalCount = summary.MismatchCount.ToString("N0"),
                MatchedCount = "0",
                UnmatchedCount = summary.MismatchCount.ToString("N0"),
                MatchRate = summary.TotalRecords > 0 ? $"{(double)summary.MismatchCount / summary.TotalRecords * 100:F1}%" : "0.0%"
            });

            ReconciliationResults.Add(new ReconciliationResultL
            {
                Category = "対応データなし (NOMATCH)",
                ProviderCount = summary.NomatchCount.ToString("N0"),
                NationalCount = "0",
                MatchedCount = "0",
                UnmatchedCount = summary.NomatchCount.ToString("N0"),
                MatchRate = summary.TotalRecords > 0 ? $"{(double)summary.NomatchCount / summary.TotalRecords * 100:F1}%" : "0.0%"
            });

            // 添加照合精度分析
            ReconciliationResults.Add(new ReconciliationResultL
            {
                Category = "照合精度分析",
                ProviderCount = "基準データ",
                NationalCount = "参考データ",
                MatchedCount = "一致数",
                UnmatchedCount = "不一致数",
                MatchRate = "一致率"
            });

            ReconciliationResultGrid.ItemsSource = ReconciliationResults;

            // 更新统计信息
            UpdateStatistics(summary);
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics(ReconciliationSummary summary)
        {
            TotalRecordsText.Text = summary.TotalRecords.ToString("N0");
            MatchedRecordsText.Text = summary.MatchCount.ToString("N0");
            UnmatchedRecordsText.Text = (summary.MismatchCount + summary.NomatchCount).ToString("N0");

            var matchRate = summary.TotalRecords > 0
                ? (double)summary.MatchCount / summary.TotalRecords * 100
                : 0;
            MatchRateText.Text = $"{matchRate:F1}%";
        }

        /// <summary>
        /// 解析数字字符串
        /// </summary>
        private int ParseNumber(string numberString)
        {
            if (int.TryParse(numberString.Replace(",", ""), out int result))
                return result;
            return 0;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing)
            {
                var result = MessageBox.Show("照合処理をキャンセルしてもよろしいですか？", "キャンセル確認",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _isProcessing = false;
                    ProcessingIndicator.Visibility = Visibility.Collapsed;
                    EmptyState.Visibility = Visibility.Visible;
                    ProcessStatusText.Text = "キャンセル";
                    UpdateUI();
                }
            }
            else
            {
                // 清除结果
                ReconciliationStats.Visibility = Visibility.Collapsed;
                EmptyState.Visibility = Visibility.Visible;
                ReconciliationResults.Clear();
                ProcessStatusText.Text = "待機中";
                ExportResultButton.IsEnabled = false;
                LastReconciliationText.Text = "未実行";
                ProcessingTimeText.Text = "-";
            }
        }

        /// <summary>
        /// 导出结果按钮点击事件
        /// </summary>
        private void ExportResultButton_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "导出照合结果",
                Filter = "Excel文件 (*.xlsx)|*.xlsx|CSV文件 (*.csv)|*.csv",
                DefaultExt = "xlsx",
                FileName = $"照合結果_{DateTime.Now:yyyyMMdd_HHmm}"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    // 这里应该实现实际的导出逻辑
                    MessageBox.Show($"照合結果をエクスポートしました：{saveFileDialog.FileName}", "エクスポート成功",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"エクスポートに失敗しました：{ex.Message}", "エラー",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            StartReconciliationButton.IsEnabled = !_isProcessing;
            MonthComboBox.IsEnabled = !_isProcessing;
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 照合结果类
    /// </summary>
    public class ReconciliationResultL
    {
        public string Category { get; set; }
        public string ProviderCount { get; set; }
        public string NationalCount { get; set; }
        public string MatchedCount { get; set; }
        public string UnmatchedCount { get; set; }
        public string MatchRate { get; set; }
    }

    /// <summary>
    /// 照合汇总结果类
    /// </summary>
    public class ReconciliationSummary
    {
        public int TotalRecords { get; set; }
        public int MatchCount { get; set; }
        public int MismatchCount { get; set; }
        public int NomatchCount { get; set; }
        public int KokuhoRenCount { get; set; }
    }
}
