﻿<UserControl x:Class="NAVI.MainControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:NAVI"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- TreeView样式 -->
        <Style x:Key="ModernTreeViewStyle" TargetType="TreeView">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="0"/>
        </Style>

        <!-- TreeViewItem样式 -->
        <Style x:Key="ModernTreeViewItemStyle" TargetType="TreeViewItem">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="0,1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TreeViewItem">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 项目头部 -->
                            <Border x:Name="ItemBorder"
                                   Grid.Row="0"
                                   Background="Transparent"
                                   CornerRadius="4"
                                   Margin="2,1">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 展开/折叠按钮 -->
                                    <ToggleButton x:Name="Expander"
                                                 Grid.Column="0"
                                                 ClickMode="Press"
                                                 IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                                 Width="16" Height="16"
                                                 Margin="4,0"
                                                 Background="Transparent"
                                                 BorderThickness="0">
                                        <ToggleButton.Style>
                                            <Style TargetType="ToggleButton">
                                                <Setter Property="Focusable" Value="False"/>
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="ToggleButton">
                                                            <Border Background="Transparent">
                                                                <Path x:Name="ExpandPath"
                                                                     HorizontalAlignment="Center"
                                                                     VerticalAlignment="Center"
                                                                     Data="M 4 6 L 6 4 L 6 8 Z"
                                                                     Fill="#FF666666"/>
                                                            </Border>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsChecked" Value="True">
                                                                    <Setter TargetName="ExpandPath" Property="Data" Value="M 4 4 L 8 6 L 4 8 Z"/>
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </ToggleButton.Style>
                                    </ToggleButton>

                                    <!-- 图标 -->
                                    <materialDesign:PackIcon x:Name="ItemIcon"
                                                           Grid.Column="1"
                                                           Kind="Folder"
                                                           Width="16" Height="16"
                                                           Margin="4,0,8,0"
                                                           VerticalAlignment="Center"
                                                           Foreground="#FF2196F3"/>

                                    <!-- 文本内容 -->
                                    <ContentPresenter x:Name="PART_Header"
                                                     Grid.Column="2"
                                                     ContentSource="Header"
                                                     VerticalAlignment="Center"
                                                     Margin="0,6"/>
                                </Grid>
                            </Border>

                            <!-- 子项容器 -->
                            <ItemsPresenter x:Name="ItemsHost"
                                           Grid.Row="1"
                                           Margin="16,0,0,0"/>
                        </Grid>

                        <ControlTemplate.Triggers>
                            <!-- 鼠标悬停效果 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ItemBorder" Property="Background" Value="#FFF0F8FF"/>
                            </Trigger>

                            <!-- 选中效果 -->
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="ItemBorder" Property="Background" Value="#FFE3F2FD"/>
                                <Setter TargetName="ItemIcon" Property="Foreground" Value="#FF1976D2"/>
                            </Trigger>

                            <!-- 没有子项时隐藏展开按钮 -->
                            <Trigger Property="HasItems" Value="False">
                                <Setter TargetName="Expander" Property="Visibility" Value="Hidden"/>
                                <Setter TargetName="ItemIcon" Property="Kind" Value="FileDocument"/>
                            </Trigger>

                            <!-- 展开状态时的图标 -->
                            <Trigger Property="IsExpanded" Value="True">
                                <Setter TargetName="ItemIcon" Property="Kind" Value="FolderOpen"/>
                            </Trigger>

                            <!-- 子项不可见时隐藏 -->
                            <Trigger Property="IsExpanded" Value="False">
                                <Setter TargetName="ItemsHost" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280"/>
            <ColumnDefinition Width="4"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧导航菜单 -->
        <Border Grid.Column="0"
               Background="White"
               BorderBrush="#FFE0E0E0"
               BorderThickness="0,0,1,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 导航标题 -->
                <Border Grid.Row="0"
                       Background="#FF2196F3"
                       Padding="16,12">
                    <TextBlock Text="首页"
                              Foreground="White"
                              FontSize="16"
                              FontWeight="Medium"/>
                </Border>

                <!-- 树形菜单 -->
                <ScrollViewer Grid.Row="1"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Disabled">
                    <TreeView Name="MenuTree"
                             SelectedItemChanged="MenuTree_SelectedItemChanged"
                             Style="{StaticResource ModernTreeViewStyle}"
                             ItemContainerStyle="{StaticResource ModernTreeViewItemStyle}"
                             Margin="8" >
                        <TreeViewItem Header="データベース" IsExpanded="True" FontSize="15">
                            <!--<TreeViewItem Header="事業者管理データ" Tag="ProviderManagementControl"/>-->
                            <TreeViewItem Header="受給者サービス情報" Tag="RecipientServiceInfoControl"/>
                            <TreeViewItem Header="国保連データ" Tag="NationalHealthDataControl"/>
                            
                        </TreeViewItem>
                        <TreeViewItem Header="処理機能" FontSize="15">
                            <TreeViewItem Header="事業者Excel取込" Tag="ProviderExcelImportControl"/>
                            <!--<TreeViewItem Header="事業者紙資料取込" Tag="ProviderDocumentImportControl"/>-->
                            <TreeViewItem Header="国保連CSV取込" Tag="NationalCsvImportControl"/>
                            <TreeViewItem Header="データ照合" Tag="DataReconciliationControl"/>
                            <TreeViewItem Header="照合結果データ" Tag="ReconciliationResultControl"/>
                            <!--<TreeViewItem Header="月次処理実行照合" Tag="MonthlyProcessingControl"/>
                            <TreeViewItem Header="データ編集" Tag="DataEditControl"/>-->
                        </TreeViewItem>
                        <TreeViewItem Header="財務状況" FontSize="15">
                            <TreeViewItem Header="財務状況統計" Tag="FinancialStatisticsControl"/>
                        </TreeViewItem>
                        <TreeViewItem Header="出力機能" FontSize="15">
                            <TreeViewItem Header="財務CSV出力" Tag="FinanceExport"/>
                            <TreeViewItem Header="補助金CSV出力" Tag="SubsidyExport"/>
                        </TreeViewItem>
                        <!--<TreeViewItem Header="月次決算処理" FontSize="15"> 
                            <TreeViewItem Header="本月处理状况" Tag="MonthStatus"/>
                        </TreeViewItem>
                        <TreeViewItem Header="今月処理状況" FontSize="15">
                            <TreeViewItem Header="本月处理状况" Tag="MonthStatus"/>
                        </TreeViewItem>-->
                        <TreeViewItem Header="マスタデータ" FontSize="15">
                            <TreeViewItem Header="事業者マスタデータ" Tag="ProviderManagementControl"/>
                            <TreeViewItem Header="受給者マスタデータ" Tag="RecipientInfoData"/>
                            <TreeViewItem Header="サービスコードマスタデータ" Tag="ServiceCodeData"/>
                            <TreeViewItem Header="職員マスタデータ" Tag="UserInfoData"/>
                        </TreeViewItem>
                        <TreeViewItem Header="システム設定" FontSize="15">
                            <TreeViewItem Header="ユーザ権限設定" Tag="UserUpdateInfo"/>
                        </TreeViewItem>
                    </TreeView>
                </ScrollViewer>
            </Grid>
        </Border>

        <!-- 分隔线 -->
        <GridSplitter Grid.Column="1"
                     HorizontalAlignment="Stretch"
                     Background="#FFE0E0E0"/>

        <!-- 右侧内容区 -->
        <Border Grid.Column="2" Background="#FFF8F9FA">
            <ContentControl Name="MainContent"/>
        </Border>
    </Grid>
</UserControl>
