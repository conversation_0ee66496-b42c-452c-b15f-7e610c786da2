using NAVI.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace NAVI.Models
{
    /// <summary>
    /// 受给者服务信息模型
    /// </summary>
    public class RecipientServiceInfo : INotifyPropertyChanged
    {
        private Dictionary<string, object> _data;

        public int No { get; set; }
        public string 登録日 { get; set; } = string.Empty;
        public string 事業者番号 { get; set; } = string.Empty;
        public string 事業者郵便番号 { get; set; } = string.Empty;
        public string 事業者住所 { get; set; } = string.Empty;
        public string 事業者名称 { get; set; } = string.Empty;
        public string 代表者名 { get; set; } = string.Empty;
        public string 代表者役職 { get; set; } = string.Empty;
        public string サービス提供年月 { get; set; } = string.Empty;
        public string 明細書件数 { get; set; }
        public string 請求金額 { get; set; }
        public string 第三者評価 { get; set; } = string.Empty;
        public string 受給者番号 { get; set; } = string.Empty;
        public string 支給決定障害者氏名 { get; set; } = string.Empty;
        public string 支給決定に係る障害児氏名 { get; set; } = string.Empty;
        public string 障害支援区分 { get; set; } = string.Empty;
        public string 事業者名称2 { get; set; } = string.Empty;
        public string 地域区分 { get; set; } = string.Empty;
        public string 旧身体療護施設区分 { get; set; } = string.Empty;
        public string 精神科医療連携体制加算 { get; set; } = string.Empty;
        public string 開始年月日 { get; set; } = string.Empty;
        public string 終了年月日 { get; set; } = string.Empty;
        public string 利用日数全体 { get; set; }
        public string サービスコード { get; set; } = string.Empty;
        public string サービス内容 { get; set; } = string.Empty;
        public string 算定単価額 { get; set; }
        public string 利用日数 { get; set; }
        public string 当月算定額 { get; set; }
        public string 摘要 { get; set; } = string.Empty;
        public string status { get; set; } = string.Empty;

        public event PropertyChangedEventHandler PropertyChanged;

        public RecipientServiceInfo()
        {
            _data = new Dictionary<string, object>();
        }

        /// <summary>
        /// 索引器，用于动态访问属性
        /// </summary>
        public object this[string propertyName]
        {
            get => _data.ContainsKey(propertyName) ? _data[propertyName] : "";
            set
            {
                _data[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// 获取所有属性名
        /// </summary>
        public IEnumerable<string> PropertyNames => _data.Keys;

        /// <summary>
        /// 从RecipientData实体创建RecipientServiceInfo
        /// </summary>
        public static RecipientServiceInfo FromRecipientData(NAVI.Services.DAL.RecipientData data)
        {
            var recipientInfo = new RecipientServiceInfo();
            recipientInfo["No"] = data.No;
            recipientInfo["登録日"] = data.登録日;
            recipientInfo["事業者番号"] = data.事業者番号;
            recipientInfo["事業者郵便番号"] = data.事業者郵便番号;
            recipientInfo["事業者住所"] = data.事業者住所;
            recipientInfo["事業者名称"] = data.事業者名称;
            recipientInfo["代表者名"] = data.代表者名;
            recipientInfo["代表者役職"] = data.代表者役職;
            recipientInfo["サービス提供年月"] = data.サービス提供年月;
            recipientInfo["明細書件数"] = data.明細書件数;
            recipientInfo["請求金額"] = data.請求金額;
            recipientInfo["第三者評価"] = data.第三者評価;
            recipientInfo["受給者番号"] = data.受給者番号;
            recipientInfo["支給決定障害者氏名"] = data.支給決定障害者氏名;
            recipientInfo["支給決定に係る障害児氏名"] = data.支給決定に係る障害児氏名;
            recipientInfo["障害支援区分"] = data.障害支援区分;
            recipientInfo["事業者名称2"] = data.事業者名称2;
            recipientInfo["地域区分"] = data.地域区分;
            recipientInfo["旧身体療護施設区分"] = data.旧身体療護施設区分;
            recipientInfo["精神科医療連携体制加算"] = data.精神科医療連携体制加算;
            recipientInfo["開始年月日"] = data.開始年月日;
            recipientInfo["終了年月日"] = data.終了年月日;
            recipientInfo["利用日数全体"] = data.利用日数全体;
            recipientInfo["サービスコード"] = data.サービスコード;
            recipientInfo["サービス内容"] = data.サービス内容;
            recipientInfo["算定単価額"] = data.算定単価額;
            recipientInfo["利用日数"] = data.利用日数;
            recipientInfo["当月算定額"] = data.当月算定額;
            recipientInfo["摘要"] = data.摘要;
            recipientInfo["status"] = data.status;

            return recipientInfo;
        }

        /// <summary>
        /// 转换为RecipientData实体
        /// </summary>
        public NAVI.Services.DAL.RecipientData ToRecipientData()
        {
            return new NAVI.Services.DAL.RecipientData
            {
                No = this["No"] != null ? Convert.ToInt32(this["No"]) : 0,
                登録日 = this["登録日"]?.ToString() ?? "",
                事業者番号 = this["事業者番号"]?.ToString() ?? "",
                事業者郵便番号 = this["事業者郵便番号"]?.ToString() ?? "",
                事業者住所 = this["事業者住所"]?.ToString() ?? "",
                事業者名称 = this["事業者名称"]?.ToString() ?? "",
                代表者名 = this["代表者名"]?.ToString() ?? "",
                代表者役職 = this["代表者役職"]?.ToString() ?? "",
                サービス提供年月 = this["サービス提供年月"]?.ToString() ?? "",
                明細書件数 = this["明細書件数"]?.ToString() ?? "",
                請求金額 = this["請求金額"]?.ToString() ?? "",
                第三者評価 = this["第三者評価"]?.ToString() ?? "",
                受給者番号 = this["受給者番号"]?.ToString() ?? "",
                支給決定障害者氏名 = this["支給決定障害者氏名"]?.ToString() ?? "",
                支給決定に係る障害児氏名 = this["支給決定に係る障害児氏名"]?.ToString() ?? "",
                障害支援区分 = this["障害支援区分"]?.ToString() ?? "",
                事業者名称2 = this["事業者名称2"]?.ToString() ?? "",
                地域区分 = this["地域区分"]?.ToString() ?? "",
                旧身体療護施設区分 = this["旧身体療護施設区分"]?.ToString() ?? "",
                精神科医療連携体制加算 = this["精神科医療連携体制加算"]?.ToString() ?? "",
                開始年月日 = this["開始年月日"]?.ToString() ?? "",
                終了年月日 = this["終了年月日"]?.ToString() ?? "",
                利用日数全体 = this["利用日数全体"]?.ToString() ?? "",
                サービスコード = this["サービスコード"]?.ToString() ?? "",
                サービス内容 = this["サービス内容"]?.ToString() ?? "",
                算定単価額 = this["算定単価額"]?.ToString() ?? "",
                利用日数 = this["利用日数"]?.ToString() ?? "",
                当月算定額 = this["当月算定額"]?.ToString() ?? "",
                摘要 = this["摘要"]?.ToString() ?? "",
                status = this["status"]?.ToString() ?? ""
            };
        }

        // 常用属性的便捷访问器
        public string 受给者ID
        {
            get => this["受给者ID"]?.ToString() ?? "";
            set => this["受给者ID"] = value;
        }

        public string 受给者姓名
        {
            get => this["受给者姓名"]?.ToString() ?? "";
            set => this["受给者姓名"] = value;
        }

        public string 生年月日
        {
            get => this["生年月日"]?.ToString() ?? "";
            set => this["生年月日"] = value;
        }

        public string 性别
        {
            get => this["性别"]?.ToString() ?? "";
            set => this["性别"] = value;
        }

        public string 住所
        {
            get => this["住所"]?.ToString() ?? "";
            set => this["住所"] = value;
        }

        public string 电话番号
        {
            get => this["电话番号"]?.ToString() ?? "";
            set => this["电话番号"] = value;
        }

        public string 服务代码
        {
            get => this["服务代码"]?.ToString() ?? "";
            set => this["服务代码"] = value;
        }

        public string 服务名称
        {
            get => this["服务名称"]?.ToString() ?? "";
            set => this["服务名称"] = value;
        }

        public string 利用开始日
        {
            get => this["利用开始日"]?.ToString() ?? "";
            set => this["利用开始日"] = value;
        }

        public string 利用结束日
        {
            get => this["利用结束日"]?.ToString() ?? "";
            set => this["利用结束日"] = value;
        }

        public string 月利用回数
        {
            get => this["月利用回数"]?.ToString() ?? "";
            set => this["月利用回数"] = value;
        }

        public string 单价
        {
            get => this["单价"]?.ToString() ?? "";
            set => this["单价"] = value;
        }

        public string 月额费用
        {
            get => this["月额费用"]?.ToString() ?? "";
            set => this["月额费用"] = value;
        }

        public string 负担额
        {
            get => this["负担额"]?.ToString() ?? "";
            set => this["负担额"] = value;
        }

        public string 状态
        {
            get => this["状态"]?.ToString() ?? "";
            set => this["状态"] = value;
        }

        public string 备注
        {
            get => this["备注"]?.ToString() ?? "";
            set => this["备注"] = value;
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 获取示例数据
        /// </summary>
        public static List<RecipientServiceInfo> GetSampleData()
        {
            return new List<RecipientServiceInfo>
            {
                new RecipientServiceInfo
                {
                    受给者ID = "R001",
                    受给者姓名 = "田中太郎",
                    生年月日 = "1980/04/15",
                    性别 = "男",
                    住所 = "东京都新宿区西新宿1-1-1",
                    电话番号 = "03-1234-5678",
                    服务代码 = "110101",
                    服务名称 = "居宅介护",
                    利用开始日 = "2023/04/01",
                    利用结束日 = "2024/03/31",
                    月利用回数 = "20",
                    单价 = "2500",
                    月额费用 = "50000",
                    负担额 = "5000",
                    状态 = "利用中",
                    备注 = "月20回利用予定"
                },
                new RecipientServiceInfo
                {
                    受给者ID = "R002",
                    受给者姓名 = "佐藤花子",
                    生年月日 = "1975/08/22",
                    性别 = "女",
                    住所 = "东京都渋谷区渋谷2-2-2",
                    电话番号 = "03-2345-6789",
                    服务代码 = "110201",
                    服务名称 = "居宅介护（生活援助）",
                    利用开始日 = "2023/05/01",
                    利用结束日 = "2024/04/30",
                    月利用回数 = "15",
                    单价 = "1800",
                    月额费用 = "27000",
                    负担额 = "2700",
                    状态 = "利用中",
                    备注 = "生活援助中心"
                },
                new RecipientServiceInfo
                {
                    受给者ID = "R003",
                    受给者姓名 = "铃木一郎",
                    生年月日 = "1965/12/03",
                    性别 = "男",
                    住所 = "东京都品川区大井1-3-3",
                    电话番号 = "03-3456-7890",
                    服务代码 = "120101",
                    服务名称 = "重度访问介护",
                    利用开始日 = "2023/06/01",
                    利用结束日 = "2024/05/31",
                    月利用回数 = "30",
                    单价 = "4200",
                    月额费用 = "126000",
                    负担额 = "12600",
                    状态 = "利用中",
                    备注 = "重度障害者支援"
                },
                new RecipientServiceInfo
                {
                    受给者ID = "R004",
                    受给者姓名 = "高橋美香",
                    生年月日 = "1970/09/10",
                    性别 = "女",
                    住所 = "东京都港区赤坂1-4-4",
                    电话番号 = "03-4567-8901",
                    服务代码 = "130101",
                    服务名称 = "同行援护",
                    利用开始日 = "2023/07/01",
                    利用结束日 = "2024/06/30",
                    月利用回数 = "8",
                    单价 = "3200",
                    月额费用 = "25600",
                    负担额 = "2560",
                    状态 = "利用中",
                    备注 = "外出支援"
                },
                new RecipientServiceInfo
                {
                    受给者ID = "R005",
                    受给者姓名 = "山田健太",
                    生年月日 = "1985/01/25",
                    性别 = "男",
                    住所 = "东京都中央区银座1-5-5",
                    电话番号 = "03-5678-9012",
                    服务代码 = "210101",
                    服务名称 = "生活介护",
                    利用开始日 = "2023/08/01",
                    利用结束日 = "2024/07/31",
                    月利用回数 = "22",
                    单价 = "6800",
                    月额费用 = "149600",
                    负担额 = "14960",
                    状态 = "利用中",
                    备注 = "日中活動支援"
                }
            };
        }
    }

    /// <summary>
    /// 受给者服务信息服务类
    /// </summary>
    public static class RecipientServiceInfoService
    {
        /// <summary>
        /// 获取示例数据
        /// </summary>
        public static List<RecipientServiceInfo> GetSampleData()
        {
            return RecipientServiceInfo.GetSampleData();
        }

        /// <summary>
        /// 从字典列表创建受给者服务信息列表
        /// </summary>
        public static List<RecipientServiceInfo> CreateFromDictionaries(List<Dictionary<string, object>> dictionaries)
        {
            var result = new List<RecipientServiceInfo>();

            foreach (var dict in dictionaries)
            {
                var info = new RecipientServiceInfo();
                foreach (var kvp in dict)
                {
                    info[kvp.Key] = kvp.Value;
                }
                result.Add(info);
            }

            return result;
        }
    }
}
