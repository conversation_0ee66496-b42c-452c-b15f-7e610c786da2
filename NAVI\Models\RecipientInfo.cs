using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace NAVI.Models
{
    /// <summary>
    /// 受給者基本情報データモデル
    /// </summary>
    public class RecipientInfo : INotifyPropertyChanged
    {
        private string _受給者番号 = string.Empty;
        private string _支給決定障害者氏名 = string.Empty;
        private string _支給決定に係る障害児氏名 = string.Empty;
        private string _改名後氏名 = string.Empty;

        /// <summary>
        /// 受給者番号
        /// </summary>
        public string 受給者番号
        {
            get => _受給者番号;
            set => SetProperty(ref _受給者番号, value);
        }

        /// <summary>
        /// 支給決定障害者氏名
        /// </summary>
        public string 支給決定障害者氏名
        {
            get => _支給決定障害者氏名;
            set => SetProperty(ref _支給決定障害者氏名, value);
        }

        /// <summary>
        /// 支給決定に係る障害児氏名
        /// </summary>
        public string 支給決定に係る障害児氏名
        {
            get => _支給決定に係る障害児氏名;
            set => SetProperty(ref _支給決定に係る障害児氏名, value);
        }

        /// <summary>
        /// 改名後氏名
        /// </summary>
        public string 改名後氏名
        {
            get => _改名後氏名;
            set => SetProperty(ref _改名後氏名, value);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// サンプルデータを取得
        /// </summary>
        public static List<RecipientInfo> GetSampleData()
        {
            return new List<RecipientInfo>
            {
                new RecipientInfo
                {
                    受給者番号 = "1234567890",
                    支給決定障害者氏名 = "鈴木太郎",
                    支給決定に係る障害児氏名 = "鈴木太郎",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567891",
                    支給決定障害者氏名 = "山本花子",
                    支給決定に係る障害児氏名 = "",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567892",
                    支給決定障害者氏名 = "伊本進介",
                    支給決定に係る障害児氏名 = "",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567893",
                    支給決定障害者氏名 = "川本真紀",
                    支給決定に係る障害児氏名 = "",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567894",
                    支給決定障害者氏名 = "小林林雄",
                    支給決定に係る障害児氏名 = "小林林子",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567895",
                    支給決定障害者氏名 = "田本洋輔",
                    支給決定に係る障害児氏名 = "",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567896",
                    支給決定障害者氏名 = "上本雅彦",
                    支給決定に係る障害児氏名 = "",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567897",
                    支給決定障害者氏名 = "松田沙織",
                    支給決定に係る障害児氏名 = "",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567898",
                    支給決定障害者氏名 = "山本宿也",
                    支給決定に係る障害児氏名 = "山本宿也",
                    改名後氏名 = ""
                },
                new RecipientInfo
                {
                    受給者番号 = "1234567899",
                    支給決定障害者氏名 = "高本憲幸",
                    支給決定に係る障害児氏名 = "",
                    改名後氏名 = ""
                }
            };
        }
    }
}
