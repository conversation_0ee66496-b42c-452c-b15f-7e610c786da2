using System;
using System.Threading.Tasks;
using NAVI.Services;
using NAVI.Services.DAL;

namespace NAVI
{
    /// <summary>
    /// 数据照合功能测试类
    /// </summary>
    public class DataReconciliationTest
    {
        private DatabaseManager _databaseManager;
        private RecipientRepository _recipientRepository;
        private KokuhoRenRepository _kokuhoRenRepository;

        public DataReconciliationTest()
        {
            _databaseManager = new DatabaseManager();
            _recipientRepository = _databaseManager.RecipientRepository;
            _kokuhoRenRepository = _databaseManager.KokuhoRenRepository;
        }

        /// <summary>
        /// 测试数据照合逻辑
        /// </summary>
        public async Task TestDataReconciliation()
        {
            try
            {
                Console.WriteLine("开始测试数据照合逻辑...");

                // 1. 测试受给者数据筛选
                var serviceMonth = "2025-01";
                var recipientData = await _recipientRepository.GetByServiceMonthAsync(serviceMonth);
                Console.WriteLine($"受给者数据筛选结果: {recipientData.Count}条");

                // 2. 测试国保联数据筛选
                var kokuhoRenData = await _kokuhoRenRepository.GetByServiceMonthAsync(serviceMonth);
                Console.WriteLine($"国保联数据筛选结果: {kokuhoRenData.Count}条");

                // 3. 测试数据匹配逻辑
                var kokuhoRenLookup = new System.Collections.Generic.Dictionary<string, KokuhoRenData>();
                foreach (var kokuhoRen in kokuhoRenData)
                {
                    var lookupKey = $"{kokuhoRen.事業者コード}_{kokuhoRen.受給者番号}_{kokuhoRen.障害支援区分}_{kokuhoRen.算定時間}_{kokuhoRen.サービスコード}_{kokuhoRen.サービス名称}_{kokuhoRen.請求年月日}";
                    if (!kokuhoRenLookup.ContainsKey(lookupKey))
                    {
                        kokuhoRenLookup[lookupKey] = kokuhoRen;
                    }
                }

                int matchCount = 0;
                int mismatchCount = 0;
                int nomatchCount = 0;

                // 4. 对每条受给者数据进行照合测试
                foreach (var recipient in recipientData)
                {
                    var lookupKey = $"{recipient.事業者番号}_{recipient.受給者番号}_{recipient.障害支援区分}_{recipient.利用日数}_{recipient.サービスコード}_{recipient.サービス内容}_{recipient.開始年月日}";

                    if (kokuhoRenLookup.TryGetValue(lookupKey, out var matchedKokuhoRen))
                    {
                        // 找到匹配记录，比较7个关键字段
                        if (CompareRecords(recipient, matchedKokuhoRen))
                        {
                            matchCount++;
                            Console.WriteLine($"完全匹配: 受給者番号={recipient.受給者番号}");
                        }
                        else
                        {
                            mismatchCount++;
                            Console.WriteLine($"部分匹配: 受給者番号={recipient.受給者番号}");
                        }
                    }
                    else
                    {
                        nomatchCount++;
                        Console.WriteLine($"无匹配: 受給者番号={recipient.受給者番号}");
                    }
                }

                Console.WriteLine($"照合结果统计:");
                Console.WriteLine($"  总记录数: {recipientData.Count}");
                Console.WriteLine($"  完全匹配: {matchCount}");
                Console.WriteLine($"  部分匹配: {mismatchCount}");
                Console.WriteLine($"  无匹配: {nomatchCount}");
                Console.WriteLine($"  匹配率: {(recipientData.Count > 0 ? (double)matchCount / recipientData.Count * 100 : 0):F1}%");

                Console.WriteLine("数据照合逻辑测试完成。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 比较受给者记录和国保联记录的7个关键字段
        /// </summary>
        private bool CompareRecords(RecipientData recipient, KokuhoRenData kokuhoRen)
        {
            try
            {
                // 比较7个关键字段：
                // 1. 事業者番号 vs 事業者コード
                // 2. 受給者番号 vs 受給者番号
                // 3. 障害支援区分 vs 障害支援区分
                // 4. 利用日数 vs 算定時間
                // 5. サービスコード vs サービスコード
                // 6. サービス内容 vs サービス名称
                // 7. 開始年月日 vs 請求年月日

                return recipient.事業者番号?.Trim() == kokuhoRen.事業者コード?.Trim() &&
                       recipient.受給者番号?.Trim() == kokuhoRen.受給者番号?.Trim() &&
                       recipient.障害支援区分?.Trim() == kokuhoRen.障害支援区分?.Trim() &&
                       recipient.利用日数?.Trim() == kokuhoRen.算定時間?.Trim() &&
                       recipient.サービスコード?.Trim() == kokuhoRen.サービスコード?.Trim() &&
                       recipient.サービス内容?.Trim() == kokuhoRen.サービス名称?.Trim() &&
                       recipient.開始年月日?.Trim() == kokuhoRen.請求年月日?.Trim();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"记录比较失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试状态更新功能
        /// </summary>
        public async Task TestStatusUpdate()
        {
            try
            {
                Console.WriteLine("开始测试状态更新功能...");

                // 获取第一条受给者数据进行测试
                var allRecipients = await _recipientRepository.GetAllAsync();
                if (allRecipients.Count > 0)
                {
                    var testRecipient = allRecipients[0];
                    Console.WriteLine($"测试记录: No={testRecipient.No}, 受給者番号={testRecipient.受給者番号}");

                    // 测试更新状态为MATCH
                    await _recipientRepository.UpdateStatusAsync(testRecipient.No, "MATCH");
                    Console.WriteLine("状态更新为MATCH成功");

                    // 验证更新结果
                    var updatedRecipient = await _recipientRepository.GetByNoAsync(testRecipient.No);
                    Console.WriteLine($"更新后状态: {updatedRecipient?.status}");

                    // 恢复原状态
                    await _recipientRepository.UpdateStatusAsync(testRecipient.No, testRecipient.status);
                    Console.WriteLine("状态已恢复");
                }

                Console.WriteLine("状态更新功能测试完成。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"状态更新测试失败: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _databaseManager?.Dispose();
        }
    }
}
