using System;
using System.Windows;

namespace NAVI
{
    /// <summary>
    /// TestMonthYearPickerWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TestMonthYearPickerWindow : Window
    {
        public TestMonthYearPickerWindow()
        {
            InitializeComponent();
        }

        private void TestMonthYearPicker_SelectedDateChanged(object sender, DateTime? selectedDate)
        {
            if (selectedDate.HasValue)
            {
                ResultTextBlock.Text = $"选择的日期: {selectedDate.Value:yyyy年MM月}\n" +
                                     $"格式化字符串: {selectedDate.Value:yyyyMM}";
            }
            else
            {
                ResultTextBlock.Text = "未选择日期";
            }
        }

        private void GetValueButton_Click(object sender, RoutedEventArgs e)
        {
            var formattedValue = TestMonthYearPicker.GetFormattedYearMonth();
            var selectedDate = TestMonthYearPicker.SelectedDate;
            
            ResultTextBlock.Text = $"当前值:\n" +
                                 $"SelectedDate: {selectedDate?.ToString("yyyy年MM月") ?? "null"}\n" +
                                 $"FormattedYearMonth: {formattedValue}";
        }

        private void SetValueButton_Click(object sender, RoutedEventArgs e)
        {
            TestMonthYearPicker.SetYearMonth("202412");
            ResultTextBlock.Text = "已设置值为 202412 (2024年12月)";
        }

        private void ClearValueButton_Click(object sender, RoutedEventArgs e)
        {
            TestMonthYearPicker.SelectedDate = null;
            ResultTextBlock.Text = "已清除选择";
        }
    }
}
