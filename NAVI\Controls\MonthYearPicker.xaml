<UserControl x:Class="NAVI.Controls.MonthYearPicker"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="40" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- 年月选择器样式 -->
        <Style x:Key="MonthYearPickerStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#FF2196F3"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 弹出窗口样式 -->
        <Style x:Key="PopupStyle" TargetType="Popup">
            <Setter Property="AllowsTransparency" Value="True"/>
            <Setter Property="PopupAnimation" Value="Fade"/>
            <Setter Property="Placement" Value="Bottom"/>
            <Setter Property="PlacementTarget" Value="{Binding ElementName=MainBorder}"/>
        </Style>

        <!-- 年月选择面板样式 -->
        <Style x:Key="SelectionPanelStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.2" ShadowDepth="2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="NavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Margin" Value="2"/>
        </Style>

        <Style x:Key="MonthButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Width" Value="60"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <!-- 主显示区域 -->
        <Border x:Name="MainBorder" Style="{StaticResource MonthYearPickerStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 显示文本 -->
                <TextBlock x:Name="DisplayTextBlock"
                          Grid.Column="0"
                          Text="{Binding DisplayText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Left"
                          Margin="12,0,0,0"
                          FontSize="13"
                          Foreground="#FF333333"/>

                <!-- 下拉箭头 -->
                <materialDesign:PackIcon Grid.Column="1" 
                                       Kind="ChevronDown" 
                                       Width="16" Height="16" 
                                       Margin="0,0,12,0" 
                                       VerticalAlignment="Center" 
                                       Foreground="#FF757575"/>
            </Grid>
        </Border>

        <!-- 弹出选择面板 -->
        <Popup x:Name="SelectionPopup" Style="{StaticResource PopupStyle}">
            <Border Style="{StaticResource SelectionPanelStyle}" Width="280" Height="200">
                <Grid Margin="12">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 年份选择区域 -->
                    <Grid Grid.Row="0" Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Button x:Name="PrevYearButton" 
                               Grid.Column="0"
                               Style="{StaticResource NavigationButtonStyle}"
                               Click="PrevYearButton_Click">
                            <materialDesign:PackIcon Kind="ChevronLeft" Width="16" Height="16"/>
                        </Button>

                        <TextBlock x:Name="YearText" 
                                  Grid.Column="1"
                                  Text="{Binding CurrentYear, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                  HorizontalAlignment="Center" 
                                  VerticalAlignment="Center"
                                  FontSize="16" 
                                  FontWeight="Medium"/>

                        <Button x:Name="NextYearButton" 
                               Grid.Column="2"
                               Style="{StaticResource NavigationButtonStyle}"
                               Click="NextYearButton_Click">
                            <materialDesign:PackIcon Kind="ChevronRight" Width="16" Height="16"/>
                        </Button>
                    </Grid>

                    <!-- 月份选择区域 -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 月份按钮 -->
                        <Button Grid.Row="0" Grid.Column="0" Content="1月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="1"/>
                        <Button Grid.Row="0" Grid.Column="1" Content="2月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="2"/>
                        <Button Grid.Row="0" Grid.Column="2" Content="3月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="3"/>
                        <Button Grid.Row="1" Grid.Column="0" Content="4月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="4"/>
                        <Button Grid.Row="1" Grid.Column="1" Content="5月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="5"/>
                        <Button Grid.Row="1" Grid.Column="2" Content="6月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="6"/>
                        <Button Grid.Row="2" Grid.Column="0" Content="7月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="7"/>
                        <Button Grid.Row="2" Grid.Column="1" Content="8月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="8"/>
                        <Button Grid.Row="2" Grid.Column="2" Content="9月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="9"/>
                        <Button Grid.Row="3" Grid.Column="0" Content="10月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="10"/>
                        <Button Grid.Row="3" Grid.Column="1" Content="11月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="11"/>
                        <Button Grid.Row="3" Grid.Column="2" Content="12月" Style="{StaticResource MonthButtonStyle}" Click="MonthButton_Click" Tag="12"/>
                    </Grid>

                    <!-- 操作按钮区域 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,12,0,0">
                        <Button Content="キャンセル" 
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Click="CancelButton_Click"
                               Margin="0,0,8,0"/>
                        <Button Content="確定" 
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Click="ConfirmButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Popup>
    </Grid>
</UserControl>
