<Window x:Class="NAVI.TestMonthYearPickerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:NAVI.Controls"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="MonthYearPicker Test" Height="300" Width="500">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" Text="MonthYearPicker 测试" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>

        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="选择年月：" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <controls:MonthYearPicker x:Name="TestMonthYearPicker" 
                                     Width="200" 
                                     SelectedDateChanged="TestMonthYearPicker_SelectedDateChanged"/>
        </StackPanel>

        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,20">
            <Button Content="获取值" Click="GetValueButton_Click" Margin="0,0,10,0"/>
            <Button Content="设置值 (202412)" Click="SetValueButton_Click" Margin="0,0,10,0"/>
            <Button Content="清除值" Click="ClearValueButton_Click"/>
        </StackPanel>

        <TextBlock x:Name="ResultTextBlock" Grid.Row="3" 
                  Text="选择结果将显示在这里..." 
                  FontSize="14" 
                  TextWrapping="Wrap"
                  VerticalAlignment="Top"/>
    </Grid>
</Window>
