using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using NAVI.Models;
using NAVI.Services;
using NAVI.Services.DAL;

namespace NAVI
{
    /// <summary>
    /// 财务状况统计页面
    /// </summary>
    public partial class FinancialStatisticsControl : UserControl
    {
        private DatabaseManager _databaseManager;
        private RecipientRepository _recipientRepository;
        private ObservableCollection<FinancialStatisticsData> _financialDataList;
        private string _selectedYear = "2025";

        private bool _isInitialized = false;

        public FinancialStatisticsControl()
        {
            InitializeComponent();

            // 初始化数据库管理器
            _databaseManager = new DatabaseManager();
            _recipientRepository = _databaseManager.Recipients;

            // 控件初始化完成后再设置标志并加载数据
            this.Loaded += (s, e) =>
            {
                _isInitialized = true;
                InitializeControls();
                LoadFinancialStatistics();
            };
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 设置筛选文本框的占位符
            if (ProviderNameFilterTextBox != null)
            {
                ProviderNameFilterTextBox.Text = "事業者名称・事業者番号で検索";
                ProviderNameFilterTextBox.Foreground = System.Windows.Media.Brushes.Gray;
            }

            // 设置月份显示ComboBox的默认选择
           /* if (MonthDisplayComboBox != null)
            {
                MonthDisplayComboBox.SelectedIndex = 0; // 默认选择"全月表示"
            }*/
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private async Task InitializeData()
        {
            try
            {

                // 加载财务统计数据
                LoadFinancialStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoadSampleData();
            }
        }

        /// <summary>
        /// 年度选择变化事件
        /// </summary>
        private void YearComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!_isInitialized)
                return; // 控件尚未加载完，忽略初始化时的事件

            if (YearComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var content = selectedItem.Content.ToString();
                // 提取年份 (例如: "令和7年度 (2025年)" -> "2025")
                var yearMatch = System.Text.RegularExpressions.Regex.Match(content, @"\((\d{4})年\)");
                if (yearMatch.Success)
                {
                    _selectedYear = yearMatch.Groups[1].Value;
                    LoadFinancialStatistics();
                }
            }
        }

        /// <summary>
        /// 加载财务统计数据
        /// </summary>
        private async void LoadFinancialStatistics()
        {
            try
            {
                // 从数据库获取受给者数据
                var recipientData = await _recipientRepository.GetAllAsync();

                // 根据选择的年度筛选数据
                var filteredData = FilterDataByYear(recipientData, _selectedYear);

                // 计算统计数据
                var statisticsData = CalculateStatistics(filteredData);

                // 更新UI
                UpdateStatisticsDisplay(statisticsData);

                // 生成月度详细数据
                var monthlyData = GenerateMonthlyData(filteredData);
                _allFinancialData = new ObservableCollection<FinancialStatisticsData>(monthlyData);
                _financialDataList = new ObservableCollection<FinancialStatisticsData>(monthlyData);
                FinancialDataGrid.ItemsSource = _financialDataList;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"財務統計データの読み込みに失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                LoadSampleData();
            }
        }

        /// <summary>
        /// 根据年度筛选数据
        /// </summary>
        private List<RecipientData> FilterDataByYear(List<RecipientData> data, string year)
        {
            return data.Where(d =>
            {
                if (string.IsNullOrEmpty(d.サービス提供年月)) return false;
                bool result = false;
                // 解析服务提供年月 (格式: YYYY-MM 或 YYYY/MM)
                var parts = d.サービス提供年月.Split(new char[] { '-', '/' });
                if (parts.Length >= 1)
                {
                    result = (parts[0] == year);
                }
                var partss = d.サービス提供年月.Substring(0, 4);
                if (!result && partss.ToString() == year)
                {
                    result = true;
                }
                return result;
            }).ToList();
        }

        /// <summary>
        /// 计算统计数据
        /// </summary>
        private (int providerCount, decimal currentMonth, decimal previousTotal, decimal nextTotal, decimal yearTotal)
            CalculateStatistics(List<RecipientData> data)
        {
            // 如果没有数据，返回默认值
            if (data == null || data.Count == 0)
            {
                return (0, 0, 0, 0, 0);
            }
            // 提取事業者番号数量
            var providerCount = data.Select(d => d.事業者番号).Distinct().Count();

            var currentMonth = DateTime.Now.Month;

            // 当前月合计
            var currentMonthAmount = data.Where(d =>
            {
                if (string.IsNullOrEmpty(d.サービス提供年月)) return false;

                var s = d.サービス提供年月;

                int month = 0;
                if (s.Length >= 6 && int.TryParse(s.Substring(4, 2), out int m1))
                {
                    month = m1;
                }
                else
                {
                    var parts = s.Split(new char[] { '-', '/' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length >= 2) int.TryParse(parts[1], out month);
                }

                return month == currentMonth;
            }).Sum(d => ParseDecimal(d.請求金額));

            // 上期合计（6~9月）
            var previousTotal = data.Where(d =>
            {
                if (string.IsNullOrEmpty(d.サービス提供年月)) return false;

                var s = d.サービス提供年月;

                int month = 0;
                if (s.Length >= 6 && int.TryParse(s.Substring(4, 2), out int m1))
                {
                    month = m1;
                }
                else
                {
                    var parts = s.Split(new char[] { '-', '/' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length >= 2) int.TryParse(parts[1], out month);
                }

                return month >= 6 && month <= 9;
            }).Sum(d => ParseDecimal(d.請求金額));

            // 下期合计（10~3月）
            var nextTotal = data.Where(d =>
            {
                if (string.IsNullOrEmpty(d.サービス提供年月)) return false;

                var s = d.サービス提供年月;

                int month = 0;
                if (s.Length >= 6 && int.TryParse(s.Substring(4, 2), out int m1))
                {
                    month = m1;
                }
                else
                {
                    var parts = s.Split(new char[] { '-', '/' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length >= 2) int.TryParse(parts[1], out month);
                }

                return month >= 10 || month <= 3;
            }).Sum(d => ParseDecimal(d.請求金額));



            /*var providerCount = data.Select(d => d.事業者番号).Distinct().Count();

            var currentMonth = DateTime.Now.Month;
            var currentMonthData = data.Where(d =>
            {
                if (string.IsNullOrEmpty(d.サービス提供年月)) return false;
                var parts = d.サービス提供年月.Split(new char[] { '-', '/' });
                return parts.Length >= 2 && int.TryParse(parts[1], out int month) && month == currentMonth;
            });

            var currentMonthAmount = currentMonthData.Sum(d => ParseDecimal(d.請求金額));

            // 上期合计 (6-9月)
            var previousTotal = data.Where(d =>
            {
                if (string.IsNullOrEmpty(d.サービス提供年月)) return false;
                var parts = d.サービス提供年月.Split(new char[] { '-', '/' });
                if (parts.Length >= 2 && int.TryParse(parts[1], out int month))
                {
                    return month >= 6 && month <= 9;
                }
                return false;
            }).Sum(d => ParseDecimal(d.請求金額));

            // 下期合计 (10-3月)
            var nextTotal = data.Where(d =>
            {
                if (string.IsNullOrEmpty(d.サービス提供年月)) return false;
                var parts = d.サービス提供年月.Split(new char[] { '-', '/' });
                if (parts.Length >= 2 && int.TryParse(parts[1], out int month))
                {
                    return month >= 10 || month <= 3;
                }
                return false;
            }).Sum(d => ParseDecimal(d.請求金額));*/

            var yearTotal = data.Sum(d => ParseDecimal(d.請求金額));

            return (providerCount, currentMonthAmount, previousTotal, nextTotal, yearTotal);
        }

        /// <summary>
        /// 解析金额字符串为decimal
        /// </summary>
        private decimal ParseDecimal(string value)
        {
            if (string.IsNullOrEmpty(value)) return 0;

            // 移除货币符号和逗号
            var cleanValue = value.Replace("¥", "").Replace(",", "").Trim();

            if (decimal.TryParse(cleanValue, out decimal result))
            {
                return result;
            }
            return 0;
        }

        /// <summary>
        /// 更新统计显示
        /// </summary>
        private void UpdateStatisticsDisplay((int providerCount, decimal currentMonth, decimal previousTotal, decimal nextTotal, decimal yearTotal) stats)
        {
            ProviderCountText.Text = stats.providerCount.ToString();
            CurrentMonthAmountText.Text = FormatCurrency(stats.currentMonth);
            PreviousTotalText.Text = FormatCurrency(stats.previousTotal);
            NextTotalText.Text = FormatCurrency(stats.nextTotal);
            YearTotalText.Text = FormatCurrency(stats.yearTotal);
        }

        /// <summary>
        /// 格式化货币显示
        /// </summary>
        private string FormatCurrency(decimal amount)
        {
            return $"¥{amount:N0}";
        }

        /// <summary>
        /// 格式化金额
        /// </summary>
        private string FormatAmount(decimal amount)
        {
            return amount > 0 ? amount.ToString("#,##0") : "0";
        }

        /// <summary>
        /// 生成月度详细数据
        /// </summary>
        private List<FinancialStatisticsData> GenerateMonthlyData(List<RecipientData> data)
        {
            var providerGroups = data.GroupBy(d => new { d.事業者番号, d.事業者名称 });
            var result = new List<FinancialStatisticsData>();

            foreach (var group in providerGroups)
            {
                var monthlyData = new FinancialStatisticsData
                {
                    事業者番号 = group.Key.事業者番号,
                    事業者名称 = group.Key.事業者名称
                };

                // 计算各月份金额 (从6月开始到3月，按日本财年)
                var monthAmounts = new decimal[12];
                for (int month = 1; month <= 12; month++)
                {
                    /*var monthData = group.Where(d =>
                    {
                        if (string.IsNullOrEmpty(d.サービス提供年月)) return false;
                        var parts = d.サービス提供年月.Split(new char[] { '-', '/' });
                        return parts.Length >= 2 && int.TryParse(parts[1], out int m) && m == month;
                    });*/
                    var monthData = group.Where(d =>
                    {
                        var value = d.サービス提供年月;

                        if (string.IsNullOrEmpty(value))
                            return false;

                        int extractedMonth = 0;

                        // 1. 如果是 "YYYYMM" 或 "YYYYMMDD" 形式
                        if (value.Length >= 6 && int.TryParse(value.Substring(4, 2), out extractedMonth))
                        {
                            // ok
                        }
                        // 2. 如果是 "YYYY-MM" 或 "YYYY/MM"
                        else
                        {
                            var parts = value.Split(new char[] { '-', '/' }, StringSplitOptions.RemoveEmptyEntries);
                            if (parts.Length >= 2)
                            {
                                int.TryParse(parts[1], out extractedMonth);
                            }
                        }

                        return extractedMonth == month;
                    });

                    monthAmounts[month - 1] = monthData.Sum(d => ParseDecimal(d.請求金額));
                }

                // 设置月份数据
                monthlyData.四月 = FormatAmount(monthAmounts[3]);
                monthlyData.五月 = FormatAmount(monthAmounts[4]);
                monthlyData.六月 = FormatAmount(monthAmounts[5]);
                monthlyData.七月 = FormatAmount(monthAmounts[6]);
                monthlyData.八月 = FormatAmount(monthAmounts[7]);
                monthlyData.九月 = FormatAmount(monthAmounts[8]);
                monthlyData.十月 = FormatAmount(monthAmounts[9]);
                monthlyData.十一月 = FormatAmount(monthAmounts[10]);
                monthlyData.十二月 = FormatAmount(monthAmounts[11]);
                monthlyData.一月 = FormatAmount(monthAmounts[0]);
                monthlyData.二月 = FormatAmount(monthAmounts[1]);
                monthlyData.三月 = FormatAmount(monthAmounts[2]);

                // 计算统计数据
                var 上期合計 = monthAmounts[3] + monthAmounts[4] + monthAmounts[5] + monthAmounts[6] + monthAmounts[7] + monthAmounts[8]; // 4-9月
                var 下期合計 = monthAmounts[9] + monthAmounts[10] + monthAmounts[11] + monthAmounts[0] + monthAmounts[1] + monthAmounts[2]; // 10-3月
                var 年度合計 = 上期合計 + 下期合計;

                monthlyData.上期合計 = FormatAmount(上期合計);
                monthlyData.下期合計 = FormatAmount(下期合計);
                monthlyData.年度合計 = FormatAmount(年度合計);

                result.Add(monthlyData);
            }

            return result.OrderBy(d => d.事業者番号).ToList();
        }

        /// <summary>
        /// 加载示例数据
        /// </summary>
        private void LoadSampleData()
        {
            // 更新统计显示
            ProviderCountText.Text = "4";
            CurrentMonthAmountText.Text = "¥0";
            PreviousTotalText.Text = "¥13,220,000";
            NextTotalText.Text = "¥17,180,000";
            YearTotalText.Text = "¥30,400,000";

            // 生成示例月度数据
            _financialDataList = new ObservableCollection<FinancialStatisticsData>
            {
                new FinancialStatisticsData
                {
                    事業者番号 = "**********",
                    事業者名称 = "サポートハウス美空",
                    六月 = "890,000", 七月 = "920,000", 八月 = "850,000", 九月 = "875,000",
                    十月 = "900,000", 十一月 = "925,000", 十二月 = "950,000", 一月 = "880,000", 二月 = "905,000", 三月 = "930,000",
                    上期合計 = "3,535,000", 下期合計 = "4,590,000", 年度合計 = "8,125,000"
                },
                new FinancialStatisticsData
                {
                    事業者番号 = "9876543210",
                    事業者名称 = "グリーンケア施設",
                    六月 = "780,000", 七月 = "795,000", 八月 = "750,000", 九月 = "765,000",
                    十月 = "800,000", 十一月 = "815,000", 十二月 = "830,000", 一月 = "770,000", 二月 = "785,000", 三月 = "810,000",
                    上期合計 = "3,090,000", 下期合計 = "4,010,000", 年度合計 = "7,100,000"
                },
                new FinancialStatisticsData
                {
                    事業者番号 = "1122334455",
                    事業者名称 = "※障害者センター",
                    六月 = "950,000", 七月 = "965,000", 八月 = "935,000", 九月 = "935,000",
                    十月 = "980,000", 十一月 = "995,000", 十二月 = "1,010,000", 一月 = "960,000", 二月 = "975,000", 三月 = "990,000",
                    上期合計 = "3,785,000", 下期合計 = "4,910,000", 年度合計 = "8,695,000"
                },
                new FinancialStatisticsData
                {
                    事業者番号 = "5566778899",
                    事業者名称 = "和光グループホーム",
                    六月 = "710,000", 七月 = "725,000", 八月 = "680,000", 九月 = "695,000",
                    十月 = "740,000", 十一月 = "755,000", 十二月 = "770,000", 一月 = "720,000", 二月 = "735,000", 三月 = "750,000",
                    上期合計 = "2,810,000", 下期合計 = "3,670,000", 年度合計 = "6,480,000"
                }
            };

            _allFinancialData = new ObservableCollection<FinancialStatisticsData>(_financialDataList);
            FinancialDataGrid.ItemsSource = _financialDataList;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
        }

        // 筛选相关字段
        private ObservableCollection<FinancialStatisticsData> _allFinancialData;

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadFinancialStatistics();
        }

        /// <summary>
        /// 事业者名称筛选文本变化事件
        /// </summary>
        private void ProviderNameFilterTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// 月份显示选择变化事件
        /// </summary>
        private void MonthDisplayComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// 事业者名称筛选文本框获得焦点事件
        /// </summary>
        private void ProviderNameFilterTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // 如果是占位符文本，则清空
                if (textBox.Text == "事業者名称・事業者番号で検索")
                {
                    textBox.Text = "";
                    textBox.Foreground = System.Windows.Media.Brushes.Black;
                }
            }
        }

        /// <summary>
        /// 事业者名称筛选文本框失去焦点事件
        /// </summary>
        private void ProviderNameFilterTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // 如果文本框为空，则显示占位符文本
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    textBox.Text = "事業者名称・事業者番号で検索";
                    textBox.Foreground = System.Windows.Media.Brushes.Gray;
                }
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            // 执行筛选操作
            ApplyFilters();

            // 可以在这里添加搜索动画或其他UI反馈
            // 暂时不显示消息框，因为筛选是实时的
        }

        /// <summary>
        /// 应用筛选条件
        /// </summary>
        private void ApplyFilters()
        {
            if (_allFinancialData == null) return;

            var filteredData = _allFinancialData.AsEnumerable();

            // 事业者名称筛选
            var providerFilter = ProviderNameFilterTextBox?.Text?.Trim();
            if (!string.IsNullOrEmpty(providerFilter) && providerFilter.ToString() != "事業者名称・事業者番号で検索")
            {
                filteredData = filteredData.Where(d =>
                    d.事業者名称.Contains(providerFilter) ||
                    d.事業者番号.Contains(providerFilter));
            }

            // 月份显示筛选
            /*var monthDisplayIndex = MonthDisplayComboBox?.SelectedIndex ?? 0;
            switch (monthDisplayIndex)
            {
                case 1: // 上期のみ (6-9月)
                    filteredData = filteredData.Where(d =>
                        ParseDecimal(d.六月) > 0 || ParseDecimal(d.七月) > 0 ||
                        ParseDecimal(d.八月) > 0 || ParseDecimal(d.九月) > 0);
                    break;
                case 2: // 下期のみ (10-3月)
                    filteredData = filteredData.Where(d =>
                        ParseDecimal(d.十月) > 0 || ParseDecimal(d.十一月) > 0 ||
                        ParseDecimal(d.十二月) > 0 || ParseDecimal(d.一月) > 0 ||
                        ParseDecimal(d.二月) > 0 || ParseDecimal(d.三月) > 0);
                    break;
                case 3: // 当月のみ
                    var currentMonth = DateTime.Now.Month;
                    filteredData = filteredData.Where(d => HasDataForMonth(d, currentMonth));
                    break;
                    // case 0: 全月表示 - 不需要额外筛选
            }*/

            _financialDataList.Clear();
            foreach (var item in filteredData)
            {
                _financialDataList.Add(item);
            }
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("エクスポート機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("印刷機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 检查指定月份是否有数据
        /// </summary>
        private bool HasDataForMonth(FinancialStatisticsData data, int month)
        {
            switch (month)
            {
                case 1:
                    return ParseDecimal(data.一月) > 0;
                case 2:
                    return ParseDecimal(data.二月) > 0;
                case 3:
                    return ParseDecimal(data.三月) > 0;
                case 6:
                    return ParseDecimal(data.六月) > 0;
                case 7:
                    return ParseDecimal(data.七月) > 0;
                case 8:
                    return ParseDecimal(data.八月) > 0;
                case 9:
                    return ParseDecimal(data.九月) > 0;
                case 10:
                    return ParseDecimal(data.十月) > 0;
                case 11:
                    return ParseDecimal(data.十一月) > 0;
                case 12:
                    return ParseDecimal(data.十二月) > 0;
                default:
                    return false;
            }
        }
    }

    /// <summary>
    /// 财务统计数据模型
    /// </summary>
    public class FinancialStatisticsData
    {
        public string 事業者番号 { get; set; } = string.Empty;
        public string 事業者名称 { get; set; } = string.Empty;
        public string 四月 { get; set; } = "0";
        public string 五月 { get; set; } = "0";
        public string 六月 { get; set; } = "0";
        public string 七月 { get; set; } = "0";
        public string 八月 { get; set; } = "0";
        public string 九月 { get; set; } = "0";
        public string 十月 { get; set; } = "0";
        public string 十一月 { get; set; } = "0";
        public string 十二月 { get; set; } = "0";
        public string 一月 { get; set; } = "0";
        public string 二月 { get; set; } = "0";
        public string 三月 { get; set; } = "0";
        public string 上期合計 { get; set; } = "0";
        public string 下期合計 { get; set; } = "0";
        public string 年度合計 { get; set; } = "0";
    }
}
