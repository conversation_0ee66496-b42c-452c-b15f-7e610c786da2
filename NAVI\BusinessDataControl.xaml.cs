﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using NAVI.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Services.DAL;
using NAVI.Windows;
using NAVI.Utils;
using System.Data.SQLite;

namespace NAVI
{
    /// <summary>
    /// BusinessDataControl.xaml 的交互逻辑
    /// </summary>
    public partial class BusinessDataControl : UserControl
    {
        private ObservableCollection<BusinessData> _businessDataList;
        private List<BusinessData> _allData;
        private List<string> _columnNames;
        private DatabaseManager _databaseManager;
        private ServiceProviderRepository _serviceProviderRepository;
        private System.Windows.Threading.DispatcherTimer _searchTimer;

        // 分页相关
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalRecords = 0;

        public BusinessDataControl()
        {
            InitializeComponent();
            InitializeData();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化数据库管理器
                _databaseManager = new DatabaseManager();
                _serviceProviderRepository = _databaseManager.ServiceProviders;

                // 从数据库加载数据
                LoadDataFromDatabase();

                // 设置搜索框的占位符效果
                SetupSearchBoxPlaceholder();

                // 初始化分页
                UpdatePagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                //LoadSampleData();
            }
        }

        /// <summary>
        /// 从数据库加载数据（异步）
        /// </summary>
        private async Task LoadDataFromDatabaseAsync()
        {
            try
            {
                // 显示加载提示
                Application.Current.Dispatcher.Invoke(() => ShowLoadingIndicator(true));

                // 设置列名
                _columnNames = new List<string>
                {
                    "No", "事業者番号", "郵便番号", "所在地", "事業者名称", "代表者役職", "代表者名",
                    "担当者氏名", "連絡先", "サービス種別", "加算対象サービス", "第三者評価結果", "研修受講証明", "利用者情報"
                };

                // 创建动态列
                Application.Current.Dispatcher.Invoke(() => CreateDynamicColumns());
                //CreateDynamicColumns();

                // 使用分页查询获取数据
                await LoadPagedDataAsync();

                // 隐藏加载提示
                Application.Current.Dispatcher.Invoke(() => ShowLoadingIndicator(false));
            }
            catch (Exception ex)
            {
                //ShowLoadingIndicator(false);
                Application.Current.Dispatcher.Invoke(() => ShowLoadingIndicator(false));
                throw new Exception($"从数据库加载数据失败：{ex.Message}");
            }
        }
        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 分页控件事件
            PaginationControl.PageChanged += PaginationControl_PageChanged;
            PaginationControl.PageSizeChanged += PaginationControl_PageSizeChanged;

            // DataGrid选择变化事件
            BusinessDataGrid.SelectionChanged += BusinessDataGrid_SelectionChanged;

            // 搜索框事件
            SearchTextBox.GotFocus += SearchTextBox_GotFocus;
            SearchTextBox.LostFocus += SearchTextBox_LostFocus;
            SearchTextBox.TextChanged += SearchTextBox_TextChanged;
        }

        /// <summary>
        /// 设置搜索框占位符效果
        /// </summary>
        private void SetupSearchBoxPlaceholder()
        {
            SearchTextBox.Foreground = Brushes.Gray;
        }

        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        /*private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "输入保险者番号・被保险者番号・氏名・其他关键字")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black);
            }
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "输入保险者番号・被保险者番号・氏名・其他关键字";
                SearchTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray);
            }
        }*/

        /// <summary>
        /// 从数据库加载数据（同步版本，保持兼容性）
        /// </summary>
        private void LoadDataFromDatabase()
        {
            try
            {
                // 设置列名
                _columnNames = new List<string>
                {
                    "No", "事業者番号", "郵便番号", "所在地", "事業者名称", "代表者役職", "代表者名",
                    "担当者氏名", "連絡先", "サービス種別", "加算対象サービス", "第三者評価結果", "研修受講証明", "利用者情報"
                };

                CreateDynamicColumns();

                // 使用异步加载
                Task.Run(async () => await LoadDataFromDatabaseAsync());
            }
            catch (Exception ex)
            {
                throw new Exception($"从数据库加载数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载分页数据
        /// </summary>
        private async Task LoadPagedDataAsync()
        {
            try
            {
                string searchText = null;

                Dispatcher.Invoke(() =>
                {
                    searchText = SearchTextBox?.Text?.Trim();
                });
                string whereClause = "";
                var parameters = new List<SQLiteParameter>();

                // 构建搜索条件
                if (!string.IsNullOrEmpty(searchText) && searchText != "事業者番号・事業者名・住所等のキーワードを入力")
                {
                    whereClause = @"""事業者番号"" LIKE @search OR ""事業者名称"" LIKE @search OR ""所在地"" LIKE @search OR
                                   ""代表者名"" LIKE @search OR ""担当者氏名"" LIKE @search";
                    parameters.Add(new SQLiteParameter("@search", $"%{searchText}%"));
                }

                // 使用分页查询
                var (serviceProviders, totalCount) = await _serviceProviderRepository.GetPagedAsync(
                    _currentPage, _pageSize, whereClause, parameters.ToArray());

                _totalRecords = totalCount;

                // 转换数据
                var businessDataList = ConvertServiceProvidersToBusinessData(serviceProviders);
                _businessDataList = new ObservableCollection<BusinessData>(businessDataList);

                // 更新UI（需要在UI线程中执行）
                Application.Current.Dispatcher.Invoke(() =>
                {
                    BusinessDataGrid.ItemsSource = _businessDataList;
                    UpdatePagination();
                    UpdateStatusInfo();
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"加载分页数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 将ServiceProvider转换为BusinessData
        /// </summary>
        private List<BusinessData> ConvertServiceProvidersToBusinessData(List<ServiceProvider> serviceProviders)
        {
            var businessDataList = new List<BusinessData>();

            foreach (var provider in serviceProviders)
            {
                var businessData = new BusinessData();
                businessData.SetProperty("No", provider.No);
                businessData.SetProperty("事業者番号", provider.事業者番号);
                businessData.SetProperty("郵便番号", provider.郵便番号);
                businessData.SetProperty("所在地", provider.所在地);
                businessData.SetProperty("事業者名称", provider.事業者名称);
                businessData.SetProperty("代表者役職", provider.代表者役職);
                businessData.SetProperty("代表者名", provider.代表者名);
                businessData.SetProperty("担当者氏名", provider.担当者氏名);
                businessData.SetProperty("連絡先", provider.連絡先);
                businessData.SetProperty("サービス種別", provider.サービス種別);
                businessData.SetProperty("加算対象サービス", provider.加算対象サービス);
                businessData.SetProperty("第三者評価結果", provider.第三者評価結果);
                businessData.SetProperty("研修受講証明", provider.研修受講証明);
                businessData.SetProperty("利用者情報", provider.利用者情報);

                businessDataList.Add(businessData);
            }

            return businessDataList;
        }

        /// <summary>
        /// 创建动态列
        /// </summary>
        private void CreateDynamicColumns()
        {
            // 清除除操作列外的所有列
            var operationColumn = BusinessDataGrid.Columns.FirstOrDefault();
            BusinessDataGrid.Columns.Clear();
            if (operationColumn != null)
            {
                BusinessDataGrid.Columns.Add(operationColumn);
            }

            // 添加动态列
            foreach (var columnName in _columnNames)
            {
                var column = new DataGridTextColumn
                {
                    Header = columnName,
                    Binding = new System.Windows.Data.Binding(columnName),
                    Width = GetColumnWidth(columnName)
                };
                BusinessDataGrid.Columns.Add(column);
            }
        }

        /// <summary>
        /// 获取列宽度
        /// </summary>
        private DataGridLength GetColumnWidth(string columnName)
        {
            // 根据列名设置合适的宽度
            if (columnName.Contains("编号") || columnName.Contains("邮政编码"))
                return new DataGridLength(100);
            else if (columnName.Contains("名称") || columnName.Contains("姓名"))
                return new DataGridLength(120);
            else if (columnName.Contains("地址") || columnName.Contains("所在地"))
                return new DataGridLength(200);
            else if (columnName.Contains("联系") || columnName.Contains("电话"))
                return new DataGridLength(120);
            else if (columnName.Contains("用户信息") || columnName.Contains("备注"))
                return new DataGridLength(180);
            else
                return new DataGridLength(120);
        }


        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "事業者番号・事業者名・住所等のキーワードを入力")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = Brushes.Black;
            }
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "事業者番号・事業者名・住所等のキーワードを入力";
                SearchTextBox.Foreground = Brushes.Gray;
            }
        }

        /// <summary>
        /// 搜索框文本变化事件
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 延迟搜索，避免频繁查询
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
            }
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                _currentPage = 1; // 重置到第一页
                ApplyPagination();
            };
            _searchTimer.Start();
        }

        /// <summary>
        /// DataGrid选择变化事件
        /// </summary>
        private void BusinessDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateStatusInfo();
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            // 使用数据库分页查询
            Task.Run(async () => await LoadPagedDataAsync());
        }

        /// <summary>
        /// 更新分页信息
        /// </summary>
        private void UpdatePagination()
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);

            PaginationControl.CurrentPage = _currentPage;
            PaginationControl.TotalPages = Math.Max(1, totalPages);
            PaginationControl.TotalRecords = _totalRecords;
            PaginationControl.PageSize = _pageSize;
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            ApplyPagination();
        }


        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            int selectedCount = BusinessDataGrid.SelectedItems.Count;

            // 尝试更新主窗口的状态栏
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.UpdateStatusBar(_totalRecords, selectedCount, PaginationControl.TotalPages, "");
        }



        /// <summary>
        /// 分页控件页码变化事件
        /// </summary>
        private void PaginationControl_PageChanged(object sender, PageChangedEventArgs e)
        {
            _currentPage = e.NewPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, PageSizeChangedEventArgs e)
        {
            _pageSize = e.NewPageSize;
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private async void AddButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new EditWindow("新規事業者データ追加", _columnNames);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    // 创建ServiceProvider对象
                    var serviceProvider = new ServiceProvider
                    {
                        事業者番号 = editWindow.ResultData.GetValueOrDefault("事業者番号", "").ToString(),
                        郵便番号 = editWindow.ResultData.GetValueOrDefault("郵便番号", "").ToString(),
                        所在地 = editWindow.ResultData.GetValueOrDefault("所在地", "").ToString(),
                        事業者名称 = editWindow.ResultData.GetValueOrDefault("事業者名称", "").ToString(),
                        代表者役職 = editWindow.ResultData.GetValueOrDefault("代表者役職", "").ToString(),
                        代表者名 = editWindow.ResultData.GetValueOrDefault("代表者名", "").ToString(),
                        担当者氏名 = editWindow.ResultData.GetValueOrDefault("担当者氏名", "").ToString(),
                        連絡先 = editWindow.ResultData.GetValueOrDefault("連絡先", "").ToString(),
                        サービス種別 = editWindow.ResultData.GetValueOrDefault("サービス種別", "").ToString(),
                        加算対象サービス = editWindow.ResultData.GetValueOrDefault("加算対象サービス", "").ToString(),
                        第三者評価結果 = editWindow.ResultData.GetValueOrDefault("第三者評価結果", "").ToString(),
                        研修受講証明 = editWindow.ResultData.GetValueOrDefault("研修受講証明", "").ToString(),
                        利用者情報 = editWindow.ResultData.GetValueOrDefault("利用者情報", "").ToString()
                    };

                    // 保存到数据库
                    await _serviceProviderRepository.CreateProviderAsync(serviceProvider);

                    // 刷新数据
                    LoadDataFromDatabase();
                    MessageBox.Show("データの追加が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ追加に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.Tag as BusinessData;
            if (selectedItem == null)
            {
                MessageBox.Show("編集するレコードを選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var editData = new Dictionary<string, object>();
                foreach (var columnName in _columnNames)
                {
                    editData[columnName] = selectedItem.GetProperty(columnName);
                }

                var editWindow = new EditWindow("事業者データ編集", _columnNames, editData);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    // 获取No值用于更新
                    var noValue = selectedItem["No"]?.ToString();
                    if (int.TryParse(noValue, out int no))
                    {
                        // 创建更新的ServiceProvider对象
                        var serviceProvider = new ServiceProvider
                        {
                            No = no,
                            事業者番号 = editWindow.ResultData.GetValueOrDefault("事業者番号", "").ToString(),
                            郵便番号 = editWindow.ResultData.GetValueOrDefault("郵便番号", "").ToString(),
                            所在地 = editWindow.ResultData.GetValueOrDefault("所在地", "").ToString(),
                            事業者名称 = editWindow.ResultData.GetValueOrDefault("事業者名称", "").ToString(),
                            代表者役職 = editWindow.ResultData.GetValueOrDefault("代表者役職", "").ToString(),
                            代表者名 = editWindow.ResultData.GetValueOrDefault("代表者名", "").ToString(),
                            担当者氏名 = editWindow.ResultData.GetValueOrDefault("担当者氏名", "").ToString(),
                            連絡先 = editWindow.ResultData.GetValueOrDefault("連絡先", "").ToString(),
                            サービス種別 = editWindow.ResultData.GetValueOrDefault("サービス種別", "").ToString(),
                            加算対象サービス = editWindow.ResultData.GetValueOrDefault("加算対象サービス", "").ToString(),
                            第三者評価結果 = editWindow.ResultData.GetValueOrDefault("第三者評価結果", "").ToString(),
                            研修受講証明 = editWindow.ResultData.GetValueOrDefault("研修受講証明", "").ToString(),
                            利用者情報 = editWindow.ResultData.GetValueOrDefault("利用者情報", "").ToString()
                        };

                        // 更新数据库
                        await _serviceProviderRepository.UpdateProviderAsync(serviceProvider);

                        // 刷新数据
                        LoadDataFromDatabase();
                        MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("レコードIDを取得できません！", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ編集に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.Tag as BusinessData;
            if (selectedItem == null)
            {
                MessageBox.Show("削除するレコードを選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show("このレコードを削除しますか？", "削除確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // 获取No值用于删除
                    var noValue = selectedItem["No"]?.ToString();
                    if (int.TryParse(noValue, out int no))
                    {
                        // 从数据库删除
                        await _serviceProviderRepository.DeleteAsync("\"No\" = @no",
                            _serviceProviderRepository.CreateParameter("@no", no));

                        // 刷新数据
                        LoadDataFromDatabase();
                        MessageBox.Show("データの削除が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("レコードIDを取得できません！", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"データ削除に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _currentPage = 1;
                ApplyPagination();
                MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ更新に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("エクスポート機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("印刷機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 显示/隐藏加载指示器
        /// </summary>
        private void ShowLoadingIndicator(bool show)
        {
            if (show)
            {
                BusinessDataGrid.Opacity = 0.5;
                // 可以在这里添加进度条或加载动画
            }
            else
            {
                BusinessDataGrid.Opacity = 1.0;
            }
        }

        /// <summary>
        /// 更新加载进度
        /// </summary>
        private void UpdateLoadingProgress(int percent)
        {
            // 可以在状态栏或其他地方显示进度
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.UpdateStatusBar(_totalRecords, 0, 0, $"加载中... {percent}%");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
        }
    }
}
