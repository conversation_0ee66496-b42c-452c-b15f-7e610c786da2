﻿<UserControl x:Class="NAVI.SubsidyCsvExportControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:NAVI"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- 成功消息面板样式 -->
        <Style x:Key="SuccessMessageStyle" TargetType="Border">
            <Setter Property="Background" Value="#FFD4EDDA"/>
            <Setter Property="BorderBrush" Value="#FFC3E6CB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,20,0,0"/>
        </Style>

        <!-- 成功消息文本样式 -->
        <Style x:Key="SuccessTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#FF155724"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <!-- 标题样式 -->
        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FF2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#FF34495E"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>

        <!-- 下拉框样式 -->
        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="36"/>
            <Setter Property="Width" Value="200"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 主要按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF2986A8"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                               Background="{TemplateBinding Background}" 
                               CornerRadius="4" 
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF1F6B85"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF1A5A73"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 次要按钮样式 -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF6C757D"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                               Background="{TemplateBinding Background}" 
                               CornerRadius="4" 
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF5A6268"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#FF545B62"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#FFF8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Border Grid.Row="0" Background="White" Padding="20" BorderBrush="#FFE9ECEF" BorderThickness="0,0,0,1">
            <TextBlock Text="補助金CSV出力" Style="{StaticResource TitleStyle}"/>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 补助金CSV输出对象期间选择区域 -->
            <Border Grid.Row="0" Background="White" Padding="20" CornerRadius="8" 
                   BorderBrush="#FFE9ECEF" BorderThickness="1" Margin="0,0,0,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 区域标题 -->
                    <TextBlock Grid.Row="0" Text="補助金CSV出力対象期間選択" 
                              FontSize="16" FontWeight="Bold" 
                              Foreground="#FF2C3E50" Margin="0,0,0,15"/>

                    <!-- 开始月份选择 -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="補助金CSV出力対象開始月選択:" Style="{StaticResource LabelStyle}"/>
                        <ComboBox x:Name="StartMonthComboBox" Style="{StaticResource ComboBoxStyle}">
                            <ComboBoxItem Content="令和7年4月" IsSelected="True"/>
                            <ComboBoxItem Content="令和7年3月"/>
                            <ComboBoxItem Content="令和7年2月"/>
                            <ComboBoxItem Content="令和7年1月"/>
                            <ComboBoxItem Content="令和6年12月"/>
                            <ComboBoxItem Content="令和6年11月"/>
                            <ComboBoxItem Content="令和6年10月"/>
                            <ComboBoxItem Content="令和6年9月"/>
                            <ComboBoxItem Content="令和6年8月"/>
                            <ComboBoxItem Content="令和6年7月"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- 结束月份选择 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,20">
                        <TextBlock Text="補助金CSV出力対象終了月選択:" Style="{StaticResource LabelStyle}"/>
                        <ComboBox x:Name="EndMonthComboBox" Style="{StaticResource ComboBoxStyle}">
                            <ComboBoxItem Content="令和7年7月" IsSelected="True"/>
                            <ComboBoxItem Content="令和7年6月"/>
                            <ComboBoxItem Content="令和7年5月"/>
                            <ComboBoxItem Content="令和7年4月"/>
                            <ComboBoxItem Content="令和7年3月"/>
                            <ComboBoxItem Content="令和7年2月"/>
                            <ComboBoxItem Content="令和7年1月"/>
                            <ComboBoxItem Content="令和6年12月"/>
                            <ComboBoxItem Content="令和6年11月"/>
                            <ComboBoxItem Content="令和6年10月"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Row="3" Orientation="Horizontal">
                        <Button x:Name="ExportButton" Content="出力実行" 
                               Style="{StaticResource PrimaryButtonStyle}" 
                               Margin="0,0,10,0" Click="ExportButton_Click"/>
                        <Button x:Name="CancelButton" Content="キャンセル" 
                               Style="{StaticResource SecondaryButtonStyle}" 
                               Click="CancelButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 成功消息区域 -->
            <Border x:Name="SuccessMessagePanel" Grid.Row="1" 
                   Style="{StaticResource SuccessMessageStyle}" 
                   Visibility="Collapsed">
                <StackPanel>
                    <TextBlock Text="処理が完了しました。" Style="{StaticResource SuccessTextStyle}"/>
                    <TextBlock x:Name="ExportResultText" Text="補助金CSVファイルが正常に出力されました。" 
                              Style="{StaticResource SuccessTextStyle}" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- 占位区域 -->
            <Grid Grid.Row="2"/>
        </Grid>
    </Grid>
</UserControl>
