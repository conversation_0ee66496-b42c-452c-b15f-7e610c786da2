using System;
using System.Threading.Tasks;
using System.Windows;
using NAVI.Models;
using NAVI.Services;
using NAVI.Services.DAL;

namespace NAVI.Windows
{
    /// <summary>
    /// UserEditWindow.xaml 的交互逻辑
    /// </summary>
    public partial class UserEditWindow : Window
    {
        private DatabaseManager _databaseManager;
        private UserRepository _userRepository;
        private UserData _editingUser;
        private bool _isEditMode;

        public UserEditWindow(string title, UserData user = null)
        {
            InitializeComponent();
            
            _databaseManager = new DatabaseManager();
            _userRepository = _databaseManager.Users;
            
            TitleTextBlock.Text = title;
            Title = title;
            
            _editingUser = user;
            _isEditMode = user != null;
            
            InitializeForm();
        }

        /// <summary>
        /// 初始化表单
        /// </summary>
        private void InitializeForm()
        {
            if (_isEditMode && _editingUser != null)
            {
                // 编辑模式，填充现有数据
                EmployeeNumberTextBox.Text = _editingUser.職員番号;
                EmployeeNumberTextBox.IsReadOnly = true; // 职员番号不允许修改
                
                DepartmentTextBox.Text = _editingUser.部署名;
                PositionTextBox.Text = _editingUser.役職;
                NameTextBox.Text = _editingUser.氏名;
                IdNumberTextBox.Text = _editingUser.ID番号;
                
                // 显示密码修改选项
                PasswordChangeGrid.Visibility = Visibility.Visible;
                PasswordBox.IsEnabled = false;
                ConfirmPasswordBox.IsEnabled = false;
            }
            else
            {
                // 新增模式
                PasswordChangeGrid.Visibility = Visibility.Collapsed;
                PasswordBox.IsEnabled = true;
                ConfirmPasswordBox.IsEnabled = true;
            }
        }

        /// <summary>
        /// 密码修改复选框选中事件
        /// </summary>
        private void ChangePasswordCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            PasswordBox.IsEnabled = true;
            ConfirmPasswordBox.IsEnabled = true;
            PasswordBox.Clear();
            ConfirmPasswordBox.Clear();
        }

        /// <summary>
        /// 密码修改复选框取消选中事件
        /// </summary>
        private void ChangePasswordCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            PasswordBox.IsEnabled = false;
            ConfirmPasswordBox.IsEnabled = false;
            PasswordBox.Clear();
            ConfirmPasswordBox.Clear();
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (!ValidateInput())
                    return;

                if (_isEditMode)
                {
                    await UpdateUser();
                }
                else
                {
                    await CreateUser();
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            // 验证必填字段
            if (string.IsNullOrWhiteSpace(EmployeeNumberTextBox.Text))
            {
                MessageBox.Show("職員番号は必須です。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                EmployeeNumberTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("氏名は必須です。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(IdNumberTextBox.Text))
            {
                MessageBox.Show("ID番号は必須です。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                IdNumberTextBox.Focus();
                return false;
            }

            // 验证密码
            if (!_isEditMode || (_isEditMode && ChangePasswordCheckBox.IsChecked == true))
            {
                if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                {
                    MessageBox.Show("パスワードは必須です。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    PasswordBox.Focus();
                    return false;
                }

                if (PasswordBox.Password != ConfirmPasswordBox.Password)
                {
                    MessageBox.Show("パスワードが一致しません。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ConfirmPasswordBox.Focus();
                    return false;
                }

                if (PasswordBox.Password.Length < 6)
                {
                    MessageBox.Show("パスワードは6文字以上である必要があります。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    PasswordBox.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 创建新用户
        /// </summary>
        private async Task CreateUser()
        {
            // 检查职员番号是否已存在
            if (await _userRepository.EmployeeNumberExistsAsync(EmployeeNumberTextBox.Text.Trim()))
            {
                MessageBox.Show("この職員番号は既に存在します。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                EmployeeNumberTextBox.Focus();
                return;
            }

            // 检查ID番号是否已存在
            if (await _userRepository.IdNumberExistsAsync(IdNumberTextBox.Text.Trim()))
            {
                MessageBox.Show("このID番号は既に存在します。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                IdNumberTextBox.Focus();
                return;
            }

            var newUser = new User
            {
                職員番号 = EmployeeNumberTextBox.Text.Trim(),
                部署名 = DepartmentTextBox.Text.Trim(),
                役職 = PositionTextBox.Text.Trim(),
                氏名 = NameTextBox.Text.Trim(),
                ID番号 = IdNumberTextBox.Text.Trim(),
                パスワード = PasswordBox.Password,
                作成日時 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                更新日時 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };

            await _userRepository.CreateUserAsync(newUser);
        }

        /// <summary>
        /// 更新用户
        /// </summary>
        private async Task UpdateUser()
        {
            // 检查ID番号是否与其他用户冲突
            var existingUser = await _userRepository.GetByIdNumberAsync(IdNumberTextBox.Text.Trim());
            if (existingUser != null && existingUser.職員番号 != _editingUser.職員番号)
            {
                MessageBox.Show("このID番号は既に他のユーザーが使用しています。", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                IdNumberTextBox.Focus();
                return;
            }

            var updatedUser = new User
            {
                職員番号 = _editingUser.職員番号, // 职员番号不变
                部署名 = DepartmentTextBox.Text.Trim(),
                役職 = PositionTextBox.Text.Trim(),
                氏名 = NameTextBox.Text.Trim(),
                ID番号 = IdNumberTextBox.Text.Trim(),
                パスワード = _editingUser.パスワード, // 默认保持原密码
                作成日時 = _editingUser.作成日時,
                更新日時 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };

            // 如果选择了修改密码
            if (ChangePasswordCheckBox.IsChecked == true)
            {
                updatedUser.パスワード = PasswordBox.Password;
            }

            await _userRepository.UpdateUserAsync(updatedUser);
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 窗口关闭时释放资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            _databaseManager?.Dispose();
            base.OnClosed(e);
        }
    }
}
