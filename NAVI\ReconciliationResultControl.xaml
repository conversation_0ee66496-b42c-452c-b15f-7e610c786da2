<UserControl x:Class="NAVI.ReconciliationResultControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:NAVI.Controls"
             mc:Ignorable="d"
             d:DesignHeight="500" d:DesignWidth="900">
    
    <UserControl.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF2986A8"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                               CornerRadius="4" 
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 次要按钮样式 -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- 成功按钮样式 -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <!-- 现代化文本框样式 -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                               BorderBrush="{TemplateBinding BorderBrush}" 
                               BorderThickness="{TemplateBinding BorderThickness}" 
                               CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                         Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#FF2986A8"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- DataGrid样式 -->
        <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFE0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#FFF0F0F0"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#FFF8F9FA"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserResizeRows" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="RowHeight" Value="75"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="ColumnWidth" Value="*"/>
        </Style>
        
        <!-- DataGrid列标题样式 -->
        <Style x:Key="ModernDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#FF37474F"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
            <Setter Property="BorderBrush" Value="#FF546E7A"/>
            <Setter Property="Height" Value="70"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="#FFF8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 顶部内容区 -->
        <Border Grid.Row="0" 
               Background="White" 
               BorderBrush="#FFE0E0E0" 
               BorderThickness="0,0,0,1" 
               Padding="24,20">
            <StackPanel>
                <!-- 页面标题 -->
                <TextBlock Text="データ照合結果" 
                          FontSize="24" 
                          FontWeight="Medium" 
                          Foreground="#FF212121" 
                          Margin="0,0,0,20"/>
                
                <!-- 操作按钮区域 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <!-- 时间筛选控件 -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,12,0">
                        <TextBlock Text="請求年月日:"
                                  VerticalAlignment="Center"
                                  Margin="0,0,8,0"
                                  FontWeight="Medium"/>
                        <DatePicker x:Name="RequestDatePicker"
                                   Width="140" Height="36"
                                   VerticalAlignment="Center"
                                   SelectedDateChanged="RequestDatePicker_SelectedDateChanged"
                                   DisplayDateStart="2020/01/01"
                                   DisplayDateEnd="2030/12/31"
                                   SelectedDateFormat="Short"/>
                    </StackPanel>

                    <!-- 开始照合按钮 -->
                    <Button Content="照合実行"
                           Style="{StaticResource SuccessButtonStyle}"
                           Margin="0,0,12,0"
                           Click="StartReconciliationButton_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                       CornerRadius="4"
                                       Padding="{TemplateBinding Padding}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="PlayCircle"
                                                               Width="16" Height="16"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,8,0"/>
                                        <ContentPresenter VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FF388E3C"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- 国保联ID搜索框 -->
                    <TextBox x:Name="NationalIdSearchBox"
                            Width="250"
                            Height="36"
                            Style="{StaticResource ModernTextBoxStyle}"
                            VerticalAlignment="Center"
                            Margin="0,0,12,0"
                            Text="国保联ID・受給者ID・事業者コード"
                            GotFocus="NationalIdSearchBox_GotFocus"
                            LostFocus="NationalIdSearchBox_LostFocus"
                            TextChanged="NationalIdSearchBox_TextChanged"/>

                    <!-- 搜索按钮 -->
                    <Button Content="検索"
                           Style="{StaticResource ModernButtonStyle}"
                           Width="80" Height="36"
                           Margin="0,0,12,0"
                           Click="SearchButton_Click"/>

                    <!-- 状态筛选下拉框 -->
                    <ComboBox x:Name="StatusFilterComboBox"
                             Width="120" Height="36"
                             Margin="0,0,12,0"
                             SelectionChanged="StatusFilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="すべての状態" IsSelected="True"/>
                        <ComboBoxItem Content="MATCH"/>
                        <ComboBoxItem Content="MISMATCH"/>
                        <ComboBoxItem Content="NOMATCH"/>
                    </ComboBox>

                    <!-- 导出按钮 -->
                    <Button Content="エクスポート"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Width="100" Height="36"
                           Margin="0,0,12,0"
                           Click="ExportButton_Click"/>

                </StackPanel>
            </StackPanel>
        </Border>
        
        <!-- 统计信息区域 - 移除，直接在分页控件中显示 -->
        

        <!-- 数据表格区域 -->
        <Border Grid.Row="2"
               Background="White"
               Margin="24,16,24,0"
               CornerRadius="8,8,0,0"
               BorderBrush="#FFE0E0E0"
               BorderThickness="1,1,1,0">
            <DataGrid x:Name="ReconciliationResultDataGrid"
                     Style="{StaticResource ModernDataGridStyle}"
                     ColumnHeaderStyle="{StaticResource ModernDataGridColumnHeaderStyle}"
                     ItemsSource="{Binding ReconciliationResults}"
                     Margin="0" >
                <DataGrid.Columns>
                    <!-- 操作列 -->
                    <DataGridTemplateColumn Header="操作" Width="180" CanUserSort="False">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="2">
                                    <Button Content="詳細" Width="75" Height="35" FontSize="13"
                                           Style="{StaticResource ModernButtonStyle}" Margin="0,0,2,0"
                                           Click="DetailButton_Click"/>
                                    <Button Content="更新" Width="75" Height="35" FontSize="13"
                                           Style="{StaticResource SecondaryButtonStyle}"
                                           Click="UpdateButton_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 序号 -->
                    <DataGridTextColumn Header="No" Binding="{Binding No}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 序号 -->
                    <DataGridTextColumn Header="国保連ID" Binding="{Binding KokuhoRenDataNo}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 序号 -->
                    <DataGridTextColumn Header="受給者ID" Binding="{Binding RecipientsDataNo}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 受給者番号对比 -->
                    <DataGridTemplateColumn Header="受給者番号&#x0A;(国保連/受給者)" Width="*" MinWidth="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="3" Padding="3" Margin="1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding RecipientNumberStatus}" Value="MATCH">
                                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding RecipientNumberStatus}" Value="MISMATCH">
                                                    <Setter Property="Background" Value="#FFFFEAEA"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding RecipientNumberStatus}" Value="NOMATCH">
                                                    <Setter Property="Background" Value="#FFF3E5F5"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <TextBlock Grid.Row="0" Text="{Binding NationalRecipientNumber}" FontSize="13"
                                                  FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Row="1" Text="/" FontSize="13" HorizontalAlignment="Center"
                                                  Margin="0,1" Foreground="#FF666666"/>
                                        <TextBlock Grid.Row="2" Text="{Binding RecipientRecipientNumber}" FontSize="13"
                                                  HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 事業者コード对比 -->
                    <DataGridTemplateColumn Header="事業者コード&#x0A;(国保連/受給者)" Width="*" MinWidth="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="3" Padding="3" Margin="1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ProviderCodeStatus}" Value="MATCH">
                                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ProviderCodeStatus}" Value="MISMATCH">
                                                    <Setter Property="Background" Value="#FFFFEAEA"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ProviderCodeStatus}" Value="NOMATCH">
                                                    <Setter Property="Background" Value="#FFF3E5F5"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <TextBlock Grid.Row="0" Text="{Binding NationalProviderCode}" FontSize="13"
                                                  FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Row="1" Text="/" FontSize="8" HorizontalAlignment="Center"
                                                  Margin="0,1" Foreground="#FF666666"/>
                                        <TextBlock Grid.Row="2" Text="{Binding RecipientProviderNumber}" FontSize="13"
                                                  HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- サービス月对比 -->
                    <DataGridTemplateColumn Header="サービス月&#x0A;(国保連/受給者)" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="3" Padding="3" Margin="1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ServiceYearStatus}" Value="MATCH">
                                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ServiceYearStatus}" Value="MISMATCH">
                                                    <Setter Property="Background" Value="#FFFFEAEA"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ServiceYearStatus}" Value="NOMATCH">
                                                    <Setter Property="Background" Value="#FFF3E5F5"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <TextBlock Grid.Row="0" Text="{Binding NationalServiceYear}" FontSize="13"
                                                  FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Row="1" Text="/" FontSize="8" HorizontalAlignment="Center"
                                                  Margin="0,1" Foreground="#FF666666"/>
                                        <TextBlock Grid.Row="2" Text="{Binding RecipientServiceYear}" FontSize="13"
                                                  HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- サービスコード对比 -->
                    <DataGridTemplateColumn Header="サービスコード&#x0A;(国保連/受給者)" Width="*" MinWidth="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="3" Padding="3" Margin="1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ServiceCodeStatus}" Value="MATCH">
                                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ServiceCodeStatus}" Value="MISMATCH">
                                                    <Setter Property="Background" Value="#FFFFEAEA"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ServiceCodeStatus}" Value="NOMATCH">
                                                    <Setter Property="Background" Value="#FFF3E5F5"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <TextBlock Grid.Row="0" Text="{Binding NationalServiceCode}" FontSize="13"
                                                  FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Row="1" Text="/" FontSize="13" HorizontalAlignment="Center"
                                                  Margin="0,1" Foreground="#FF666666"/>
                                        <TextBlock Grid.Row="2" Text="{Binding RecipientServiceCode}" FontSize="13"
                                                  HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- サービス名称对比 -->
                    <DataGridTemplateColumn Header="サービス名称&#x0A;(国保連/受給者)" Width="*" MinWidth="180">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="3" Padding="3" Margin="1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ServiceNameStatus}" Value="MATCH">
                                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ServiceNameStatus}" Value="MISMATCH">
                                                    <Setter Property="Background" Value="#FFFFEAEA"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ServiceNameStatus}" Value="NOMATCH">
                                                    <Setter Property="Background" Value="#FFF3E5F5"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <TextBlock Grid.Row="0" Text="{Binding NationalServiceName}" FontSize="11"
                                                  FontWeight="Medium" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                        <TextBlock Grid.Row="1" Text="/" FontSize="11" HorizontalAlignment="Center"
                                                  Margin="0,1" Foreground="#FF666666"/>
                                        <TextBlock Grid.Row="2" Text="{Binding RecipientServiceContent}" FontSize="11"
                                                  HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 算定時間对比 -->
                    <DataGridTemplateColumn Header="算定時間&#x0A;(国保連/受給者)" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="3" Padding="2" Margin="1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding TimeStatus}" Value="MATCH">
                                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding TimeStatus}" Value="MISMATCH">
                                                    <Setter Property="Background" Value="#FFFFEAEA"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding TimeStatus}" Value="NOMATCH">
                                                    <Setter Property="Background" Value="#FFF3E5F5"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <TextBlock Grid.Row="0" Text="{Binding NationalCalculatedTime}" FontSize="13"
                                                  FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Row="1" Text="/" FontSize="13" HorizontalAlignment="Center"
                                                  Margin="0,1" Foreground="#FF666666"/>
                                        <TextBlock Grid.Row="2" Text="{Binding RecipientUsageDays}" FontSize="13"
                                                  HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 利用日数对比 -->
                    <DataGridTemplateColumn Header="利用日数&#x0A;(国保連/受給者)" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="3" Padding="2" Margin="1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CountStatus}" Value="MATCH">
                                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CountStatus}" Value="MISMATCH">
                                                    <Setter Property="Background" Value="#FFFFEAEA"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CountStatus}" Value="NOMATCH">
                                                    <Setter Property="Background" Value="#FFF3E5F5"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <TextBlock Grid.Row="0" Text="{Binding NationalCount}" FontSize="13"
                                                  FontWeight="Medium" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Row="1" Text="/" FontSize="13" HorizontalAlignment="Center"
                                                  Margin="0,1" Foreground="#FF666666"/>
                                        <TextBlock Grid.Row="2" Text="{Binding RecipientUsageCount}" FontSize="13"
                                                  HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 照合状态 -->
                    <DataGridTemplateColumn Header="照合状態" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="6" Padding="4,2" HorizontalAlignment="Center" Margin="2">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding OverallMatchStatus}" Value="MATCH">
                                                    <Setter Property="Background" Value="#FFE8F5E8"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding OverallMatchStatus}" Value="MISMATCH">
                                                    <Setter Property="Background" Value="#FFFFEAEA"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding OverallMatchStatus}" Value="NOMATCH">
                                                    <Setter Property="Background" Value="#FFF3E5F5"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Text="{Binding OverallMatchStatus}" FontSize="13" FontWeight="Medium">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding OverallMatchStatus}" Value="MATCH">
                                                        <Setter Property="Foreground" Value="#FF4CAF50"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding OverallMatchStatus}" Value="MISMATCH">
                                                        <Setter Property="Foreground" Value="#FFF44336"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding OverallMatchStatus}" Value="NOMATCH">
                                                        <Setter Property="Foreground" Value="#FF9C27B0"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 处理时间 -->
                    <DataGridTextColumn Header="処理時間" Binding="{Binding ProcessingTime}" Width="110">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="13"/>
                                <Setter Property="Foreground" Value="#FF666666"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
        

        <!-- 分页控件 -->
        <controls:PaginationControl x:Name="PaginationControl"
                                   Grid.Row="3"
                                   Margin="24,0,24,24"
                                   PageChanged="PaginationControl_PageChanged"
                                   PageSizeChanged="PaginationControl_PageSizeChanged"/>
    </Grid>
</UserControl>
