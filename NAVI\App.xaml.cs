﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;

namespace NAVI
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {

        public App()
        {
            AppDomain.CurrentDomain.AssemblyResolve += ResolveFromDllsFolder;

            string message = Utils.ClaudeOcrHelper.CreateOcrPrompt();
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            //AppDomain.CurrentDomain.AssemblyResolve += ResolveFromDllsFolder;

            // 检查是否有测试参数
            if (e.Args.Length > 0 && e.Args[0] == "--test-monthyearpicker")
            {
                var testWindow = new TestMonthYearPickerWindow();
                testWindow.Show();
                return;
            }

            // 创建测试Excel文件（如果不存在）
            try
            {
                var excelPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database", "shortstay_app_ver0.9.xlsx");
                if (!File.Exists(excelPath))
                {
                    //TestExcelCreator.CreateTestExcelFile();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建测试Excel文件失败：{ex.Message}", "警告",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private Assembly ResolveFromDllsFolder(object sender, ResolveEventArgs args)
        {
            var dllName = new AssemblyName(args.Name).Name + ".dll";
            var dllFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "dlls");
            var dllPath = Path.Combine(dllFolder, dllName);

            return File.Exists(dllPath) ? Assembly.LoadFrom(dllPath) : null;
        }
    }
}
