using System;
using System.Windows;
using System.Windows.Controls;
using NAVI.Controls;

namespace NAVI
{
    /// <summary>
    /// 简单的测试程序类
    /// </summary>
    public class TestProgram
    {
        [STAThread]
        public static void Main(string[] args)
        {
            // 检查是否有测试参数
            if (args.Length > 0 && args[0] == "--test-simple")
            {
                var app = new Application();
                var window = new SimpleTest();
                app.Run(window);
                return;
            }

            // 检查是否有MonthYearPicker测试参数
            if (args.Length > 0 && args[0] == "--test-monthyearpicker")
            {
                var app = new Application();
                var window = new TestMonthYearPickerWindow();
                app.Run(window);
                return;
            }

            // 默认启动主程序
            var mainApp = new App();
            mainApp.InitializeComponent();
            mainApp.Run();
        }
    }
}
