using NAVI.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Services.DAL;
using NAVI.Utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace NAVI
{
    /// <summary>
    /// 受给者与国保联对照数据结果控件
    /// </summary>
    public partial class ReconciliationResultControl : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<ReconciliationResult> _reconciliationResults;
        private List<ReconciliationResult> _allResults;
        private bool _isProcessing = false;
        private int _currentPage = 1;
        private int _pageSize = 50;
        private System.Windows.Threading.DispatcherTimer _searchTimer;
        private const string ExcelFilePath = "database/shortstay_app_ver0.9.xlsx";

        // 数据库相关
        private DatabaseManager _databaseManager;
        private RecipientRepository _recipientRepository;
        private KokuhoRenRepository _kokuhoRenRepository;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 照合结果列表
        /// </summary>
        public ObservableCollection<ReconciliationResult> ReconciliationResults
        {
            get => _reconciliationResults;
            set
            {
                _reconciliationResults = value;
                OnPropertyChanged(nameof(ReconciliationResults));
            }
        }

        public ReconciliationResultControl()
        {
            InitializeComponent();
            InitializeData();

            // 设置默认日期为今天
            RequestDatePicker.SelectedDate = DateTime.Today;

            // 初始化搜索框占位符样式
            InitializeSearchBox();
        }

        /// <summary>
        /// 初始化搜索框
        /// </summary>
        private void InitializeSearchBox()
        {
            if (NationalIdSearchBox.Text == "国保联ID・受給者ID・事業者コード")
            {
                NationalIdSearchBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化数据库管理器
                _databaseManager = new DatabaseManager();
                _recipientRepository = _databaseManager.Recipients;
                _kokuhoRenRepository = _databaseManager.KokuhoRenData;

                ReconciliationResults = new ObservableCollection<ReconciliationResult>();
                _allResults = new List<ReconciliationResult>();
                DataContext = this;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库初始化失败: {ex.Message}");
                MessageBox.Show($"データベース初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }

            // 初始化分页控件
            PaginationControl.PageSize = _pageSize;
            PaginationControl.CurrentPage = _currentPage;
            PaginationControl.TotalRecords = 0;

            // 执行默认查询（基于已匹配的数据）
            _ = Task.Run(async () =>
            {
                try
                {
                    await LoadDefaultDataAsync();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"默认数据加载失败: {ex.Message}");
                    // 如果默认加载失败，加载示例数据
                    Dispatcher.Invoke(() =>
                    {
                        LoadSampleData();
                        UpdateStatistics();
                        ApplyPagination();
                    });
                }
            });
        }

        /// <summary>
        /// 加载默认数据（基于已匹配的数据）
        /// </summary>
        private async Task LoadDefaultDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // 1. 读取国保连数据（应用时间筛选）
                    var nationalData = LoadNationalData();

                    // 2. 读取受给者服务信息数据
                    var recipientData = LoadRecipientServiceData();

                    // 3. 基于已有状态生成照合结果
                    _allResults = GenerateReconciliationFromExistingStatus(nationalData, recipientData);

                    // 4. 应用分页显示
                    Dispatcher.Invoke(() =>
                    {
                        ApplyPagination();
                        UpdateStatistics();
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"默认数据加载失败: {ex.Message}");
                    throw;
                }
            });
        }
        private List<ReconciliationResult> GenerateReconciliationFromExistingStatus(List<NationalData> nationalData, List<RecipientServiceInfo> recipientData)
        {
            var results = new List<ReconciliationResult>();
            int currentNo = 1;

            System.Diagnostics.Debug.WriteLine($"基于受给者数据生成照合结果 - 国保联数据: {nationalData.Count}条, 受给者数据: {recipientData.Count}条");

            foreach (var recipient in recipientData)
            {
                // 获取受给者数据关键字段
                var recipientRecipientNumber = recipient["受給者番号"]?.ToString() ?? "";
                var recipientProviderNumber = recipient["事業者番号"]?.ToString() ?? "";
                var recipientServiceYear = recipient["サービス提供年月"]?.ToString() ?? "";
                var recipientServiceCode = recipient["サービスコード"]?.ToString() ?? "";
                var recipientServiceContent = recipient["サービス内容"]?.ToString() ?? "";
                var recipientUsageDays = recipient["利用日数"]?.ToString() ?? "";
                var recipientUsageCount = recipient["利用日数"]?.ToString() ?? "";
                var recipientStatus = recipient["status"]?.ToString() ?? "";
                int recipientNo = Convert.ToInt32(recipient["No"].ToString());

                // 匹配国保联数据
                var matchedNational = nationalData.FirstOrDefault(n =>
                    n.受給者番号?.ToString() == recipientRecipientNumber &&
                    n.事業者コード?.ToString() == recipientProviderNumber &&
                    n.サービスコード?.ToString() == recipientServiceCode);

                // 初始化国保联字段
                string nationalRecipientNumber = "";
                string nationalProviderCode = "";
                string nationalServiceYear = "";
                string nationalServiceCode = "";
                string nationalServiceName = "";
                string nationalCalculatedTime = "";
                string nationalCount = "";
                string nationalStatus = "";
                int nationalNo = 0;

                if (matchedNational != null)
                {
                    nationalRecipientNumber = matchedNational.受給者番号?.ToString() ?? "";
                    nationalProviderCode = matchedNational.事業者コード?.ToString() ?? "";
                    nationalServiceYear = matchedNational.サービス提供年月?.ToString() ?? "";
                    nationalServiceCode = matchedNational.サービスコード?.ToString() ?? "";
                    nationalServiceName = matchedNational.サービス名称?.ToString() ?? "";
                    nationalCalculatedTime = matchedNational.算定時間?.ToString() ?? "";
                    nationalCount = matchedNational.回数?.ToString() ?? "";
                    nationalStatus = matchedNational.status?.ToString() ?? "";
                    nationalNo = matchedNational.No == 0 ? 0 : matchedNational.No;
                }

                // 创建照合结果
                var result = new ReconciliationResult
                {
                    No = currentNo,
                    RecipientsDataNo = recipientNo,
                    KokuhoRenDataNo = nationalNo,
                    NationalRecipientNumber = nationalRecipientNumber,
                    NationalProviderCode = nationalProviderCode,
                    NationalServiceYear = nationalServiceYear,
                    NationalServiceCode = nationalServiceCode,
                    NationalServiceName = nationalServiceName,
                    NationalCalculatedTime = nationalCalculatedTime,
                    NationalCount = nationalCount,

                    RecipientRecipientNumber = recipientRecipientNumber,
                    RecipientProviderNumber = recipientProviderNumber,
                    RecipientServiceYear = recipientServiceYear,
                    RecipientServiceCode = recipientServiceCode,
                    RecipientServiceContent = recipientServiceContent,
                    RecipientUsageDays = recipientUsageDays,
                    RecipientUsageCount = recipientUsageCount,

                    ProcessingTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                // 字段比对
                result.RecipientNumberStatus = CompareFields(nationalRecipientNumber, recipientRecipientNumber);
                result.ProviderCodeStatus = CompareFields(nationalProviderCode, recipientProviderNumber);
                result.ServiceYearStatus = CompareFields(nationalServiceYear, recipientServiceYear);
                result.ServiceCodeStatus = CompareFields(nationalServiceCode, recipientServiceCode);
                result.ServiceNameStatus = CompareFields(nationalServiceName, recipientServiceContent);
                result.TimeStatus = CompareFields(nationalCalculatedTime, recipientUsageDays);
                result.CountStatus = CompareFields(nationalCount, recipientUsageCount);

                // 状态判定
                if (!string.IsNullOrEmpty(recipientStatus) &&
                    (recipientStatus == "MATCH" || recipientStatus == "MISMATCH" || recipientStatus == "NOMATCH"))
                {
                    result.OverallMatchStatus = recipientStatus;
                }
                else if (!string.IsNullOrEmpty(nationalStatus) &&
                         (nationalStatus == "MATCH" || nationalStatus == "MISMATCH" || nationalStatus == "NOMATCH"))
                {
                    result.OverallMatchStatus = nationalStatus;
                }
                else
                {
                    // 重新计算匹配状态
                    if (matchedNational == null)
                    {
                        result.OverallMatchStatus = "NOMATCH";
                    }
                    else
                    {
                        bool isCompleteMatch =
                            result.RecipientNumberStatus == "MATCH" &&
                            result.ProviderCodeStatus == "MATCH" &&
                            result.ServiceYearStatus == "MATCH" &&
                            result.ServiceCodeStatus == "MATCH" &&
                            result.ServiceNameStatus == "MATCH" &&
                            result.TimeStatus == "MATCH" &&
                            result.CountStatus == "MATCH";

                        result.OverallMatchStatus = isCompleteMatch ? "MATCH" : "MISMATCH";
                    }
                }

                results.Add(result);
                currentNo++;
            }

            System.Diagnostics.Debug.WriteLine($"照合结果生成完毕，共 {results.Count} 条");
            return results;
        }


        /// <summary>
        /// 基于已有状态生成照合结果
        /// </summary>
        private List<ReconciliationResult> GenerateReconciliationFromExistingStatus_old(List<NationalData> nationalData, List<RecipientServiceInfo> recipientData)
        {
            var results = new List<ReconciliationResult>();
            int currentNo = 1;

            System.Diagnostics.Debug.WriteLine($"基于已有状态生成照合结果 - 国保联数据: {nationalData.Count}条, 受给者数据: {recipientData.Count}条");

            foreach (var national in nationalData)
            {
                // 获取国保联数据的关键字段
                var nationalRecipientNumber = national["受給者番号"]?.ToString() ?? "";
                var nationalProviderCode = national["事業者コード"]?.ToString() ?? "";
                var nationalServiceYear = national["サービス提供年月"]?.ToString() ?? "";
                var nationalServiceCode = national["サービスコード"]?.ToString() ?? "";
                var nationalServiceName = national["サービス名称"]?.ToString() ?? "";
                var nationalCalculatedTime = national["算定時間"]?.ToString() ?? "";
                var nationalCount = national["回数"]?.ToString() ?? "";
                var nationalStatus = national["status"]?.ToString() ?? "";

                // 通过三字段联合匹配查找受给者数据
                var matchedRecipient = recipientData.FirstOrDefault(r =>
                    r["受給者番号"]?.ToString() == nationalRecipientNumber &&
                    r["事業者番号"]?.ToString() == nationalProviderCode &&
                    r["サービスコード"]?.ToString() == nationalServiceCode);

                string recipientRecipientNumber = "";
                string recipientProviderNumber = "";
                string recipientServiceYear = "";
                string recipientServiceCode = "";
                string recipientServiceContent = "";
                string recipientUsageDays = "";
                string recipientUsageCount = "";
                string recipientStatus = "";

                if (matchedRecipient != null)
                {
                    recipientRecipientNumber = matchedRecipient["受給者番号"]?.ToString() ?? "";
                    recipientProviderNumber = matchedRecipient["事業者番号"]?.ToString() ?? "";
                    recipientServiceYear = matchedRecipient["サービス提供年月"]?.ToString() ?? "";
                    recipientServiceCode = matchedRecipient["サービスコード"]?.ToString() ?? "";
                    recipientServiceContent = matchedRecipient["サービス内容"]?.ToString() ?? "";
                    recipientUsageDays = matchedRecipient["利用日数"]?.ToString() ?? "";
                    recipientUsageCount = matchedRecipient["利用日数"]?.ToString() ?? "";
                    recipientStatus = matchedRecipient["status"]?.ToString() ?? "";
                }

                // 创建照合结果
                var result = new ReconciliationResult
                {
                    No = currentNo,
                    NationalRecipientNumber = nationalRecipientNumber,
                    NationalProviderCode = nationalProviderCode,
                    NationalServiceYear = nationalServiceYear,
                    NationalServiceCode = nationalServiceCode,
                    NationalServiceName = nationalServiceName,
                    NationalCalculatedTime = nationalCalculatedTime,
                    NationalCount = nationalCount,

                    RecipientRecipientNumber = recipientRecipientNumber,
                    RecipientProviderNumber = recipientProviderNumber,
                    RecipientServiceYear = recipientServiceYear,
                    RecipientServiceCode = recipientServiceCode,
                    RecipientServiceContent = recipientServiceContent,
                    RecipientUsageDays = recipientUsageDays,
                    RecipientUsageCount = recipientUsageCount,

                    ProcessingTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                // 计算各字段的匹配状态
                result.RecipientNumberStatus = CompareFields(nationalRecipientNumber, recipientRecipientNumber);
                result.ProviderCodeStatus = CompareFields(nationalProviderCode, recipientProviderNumber);
                result.ServiceYearStatus = CompareFields(nationalServiceYear, recipientServiceYear);
                result.ServiceCodeStatus = CompareFields(nationalServiceCode, recipientServiceCode);
                result.ServiceNameStatus = CompareFields(nationalServiceName, recipientServiceContent);
                result.TimeStatus = CompareFields(nationalCalculatedTime, recipientUsageDays);
                result.CountStatus = CompareFields(nationalCount, recipientUsageCount);

                // 使用已有的状态，如果没有则重新计算
                if (!string.IsNullOrEmpty(nationalStatus) && (nationalStatus == "MATCH" || nationalStatus == "MISMATCH" || nationalStatus == "NOMATCH"))
                {
                    result.OverallMatchStatus = nationalStatus;
                }
                else if (!string.IsNullOrEmpty(recipientStatus) && (recipientStatus == "MATCH" || recipientStatus == "MISMATCH" || recipientStatus == "NOMATCH"))
                {
                    result.OverallMatchStatus = recipientStatus;
                }
                else
                {
                    // 重新计算状态
                    if (matchedRecipient == null)
                    {
                        result.OverallMatchStatus = "NOMATCH";
                    }
                    else
                    {
                        bool isCompleteMatch = result.RecipientNumberStatus == "MATCH" &&
                                              result.ProviderCodeStatus == "MATCH" &&
                                              result.ServiceYearStatus == "MATCH" &&
                                              result.ServiceCodeStatus == "MATCH" &&
                                              result.ServiceNameStatus == "MATCH" &&
                                              result.TimeStatus == "MATCH" &&
                                              result.CountStatus == "MATCH";

                        result.OverallMatchStatus = isCompleteMatch ? "MATCH" : "MISMATCH";
                    }
                }

                results.Add(result);
                currentNo++;
            }

            System.Diagnostics.Debug.WriteLine($"基于已有状态生成照合结果完成，共生成 {results.Count} 条结果");
            return results;
        }

        /// <summary>
        /// 加载示例数据
        /// </summary>
        private void LoadSampleData()
        {
            // 初始化为空，等待用户点击照合按钮
            _allResults = new List<ReconciliationResult>();
        }

        /// <summary>
        /// 开始照合按钮点击事件
        /// </summary>
        private async void StartReconciliationButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing) return;

            try
            {
                _isProcessing = true;

                // 显示处理中状态
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "処理中...";

                // 执行照合处理
                await PerformReconciliationAsync();

                // 更新统计信息和分页
                UpdateStatistics();
                ApplyPagination();

                MessageBox.Show("データ照合が完了しました。", "照合完了",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"照合処理中にエラーが発生しました: {ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isProcessing = false;
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "照合開始";
            }
        }

        /// <summary>
        /// 执行照合处理
        /// </summary>
        private async Task PerformReconciliationAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // 1. 读取国保连数据
                    var nationalData = LoadNationalData();

                    // 2. 读取受给者服务信息数据
                    var recipientData = LoadRecipientServiceData();

                    // 3. 执行照合
                    _allResults = PerformDataReconciliation(nationalData, recipientData);

                    // 4. 更新匹配结果到数据库
                    UpdateMatchResultsToDatabase(_allResults);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"照合处理失败: {ex.Message}");
                    throw;
                }
            });
        }

        /// <summary>
        /// 读取国保连数据
        /// </summary>
        private List<NationalData> LoadNationalData()
        {
            try
            {
                // 从数据库读取国保联数据
                var kokuhoRenData = Task.Run(async () => await LoadKokuhoRenDataFromDatabase()).Result;

                // 转换为NationalData格式
                var result = ConvertKokuhoRenToNationalData(kokuhoRenData);
                System.Diagnostics.Debug.WriteLine($"从数据库加载国保联数据: {result.Count}条");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"国保连数据读取失败: {ex.Message}，使用示例数据");
                return GetNationalSampleData();
            }
        }

        /// <summary>
        /// 从数据库加载国保联数据
        /// </summary>
        private async Task<List<KokuhoRenData>> LoadKokuhoRenDataFromDatabase()
        {
            try
            {
                DateTime? selectedDate = null;

                // 在UI线程中获取选择的日期
                Dispatcher.Invoke(() =>
                {
                    selectedDate = RequestDatePicker.SelectedDate;
                });

                if (!selectedDate.HasValue)
                {
                    // 如果没有选择日期，返回所有数据
                    return await _kokuhoRenRepository.GetAllAsync();
                }

                // 根据选择的日期筛选数据
                var filterDate = selectedDate.Value;
                var serviceMonth = $"{filterDate.Year:D4}-{filterDate.Month:D2}";

                return await _kokuhoRenRepository.GetByServiceMonthAsync(serviceMonth);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从数据库加载国保联数据失败: {ex.Message}");
                return new List<KokuhoRenData>();
            }
        }

        /// <summary>
        /// 将KokuhoRenData转换为NationalData格式
        /// </summary>
        private List<NationalData> ConvertKokuhoRenToNationalData(List<KokuhoRenData> kokuhoRenData)
        {
            //var result = new List<NationalData>();

            var nationalDataList = new List<NationalData>();

            foreach (var data in kokuhoRenData)
            {
                var nationalData = new NationalData();
                nationalData.SetProperty("No", data.No);
                nationalData.SetProperty("サービス提供年月", data.サービス提供年月);
                nationalData.SetProperty("請求年月日", data.請求年月日);
                nationalData.SetProperty("請求回数", data.請求回数);
                nationalData.SetProperty("審査年月", data.審査年月);
                nationalData.SetProperty("事業者コード", data.事業者コード);
                nationalData.SetProperty("事業者名称", data.事業者名称);
                nationalData.SetProperty("受給者番号", data.受給者番号);
                nationalData.SetProperty("受給者名称", data.受給者名称);
                nationalData.SetProperty("受給者名称カナ", data.受給者名称カナ);
                nationalData.SetProperty("児童名称", data.児童名称);
                nationalData.SetProperty("児童名称カナ", data.児童名称カナ);
                nationalData.SetProperty("身体", data.身体);
                nationalData.SetProperty("知的", data.知的);
                nationalData.SetProperty("精神", data.精神);
                nationalData.SetProperty("難病", data.難病);
                nationalData.SetProperty("単価障害程度区分", data.単価障害程度区分);
                nationalData.SetProperty("障害支援区分", data.障害支援区分);
                nationalData.SetProperty("サービスコード", data.サービスコード);
                nationalData.SetProperty("サービス名称", data.サービス名称);
                nationalData.SetProperty("算定時間", data.算定時間);
                nationalData.SetProperty("回数", data.回数);
                nationalData.SetProperty("算定時間x回数", data.算定時間x回数);
                nationalData.SetProperty("単位数", data.単位数);
                nationalData.SetProperty("サービス単位", data.サービス単位);
                nationalData.SetProperty("連合会審査区分名称", data.連合会審査区分名称);
                nationalData.SetProperty("審査区分名称", data.審査区分名称);
                nationalData.SetProperty("返戻事由名称", data.返戻事由名称);
                nationalData.SetProperty("判定フラグ", data.判定フラグ);
                nationalData.SetProperty("status", data.status);

                nationalDataList.Add(nationalData);
            }

            return nationalDataList;
        }

        /// <summary>
        /// 应用日期筛选
        /// </summary>
        private List<Dictionary<string, object>> ApplyDateFilter(List<Dictionary<string, object>> data)
        {
            DateTime? selectedDate = null;

            // 在UI线程中获取选择的日期
            Dispatcher.Invoke(() =>
            {
                selectedDate = RequestDatePicker.SelectedDate;
            });

            if (!selectedDate.HasValue)
            {
                return data; // 如果没有选择日期，返回所有数据
            }

            var filterDate = selectedDate.Value;
            DateTime startDate, endDate;
            //string status = StatusFilterComboBox.Text;

            // 根据选择的日期确定筛选范围
            if (filterDate.Day <= 25)
            {
                // 如果选择的是25号之前，筛选当前月份1号到选择的日子之间的数据
                startDate = new DateTime(filterDate.Year, filterDate.Month, 1);
                endDate = filterDate;
            }
            else
            {
                // 如果超过25号，筛选1-25号的数据
                startDate = new DateTime(filterDate.Year, filterDate.Month, 1);
                endDate = new DateTime(filterDate.Year, filterDate.Month, 25);
            }

            System.Diagnostics.Debug.WriteLine($"日期筛选范围: {startDate:yyyy/MM/dd} - {endDate:yyyy/MM/dd}");

            return data.Where(item =>
            {
                var requestDateStr = item.ContainsKey("請求年月日") ? item["請求年月日"]?.ToString() : "";
                if (string.IsNullOrEmpty(requestDateStr))
                    return false;

                if (DateTime.TryParse(requestDateStr, out DateTime requestDate))
                {
                    return requestDate >= startDate && requestDate <= endDate;
                }
                /*if (status!= "すべての状態")
                {
                    return requestDate == startDate && requestDate <= endDate;
                }*/
                return false;
            }).ToList();
        }

        /// <summary>
        /// 获取国保联示例数据
        /// </summary>
        private List<NationalData> GetNationalSampleData()
        {
            var sampleData = new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    ["No"] = 1,
                    ["受給者番号"] = "1234567890",
                    ["事業者コード"] = "1310100001",
                    ["サービス提供年月"] = "202505",
                    ["サービスコード"] = "010101",
                    ["サービス名称"] = "福祉短期入所Ⅰ６",
                    ["算定時間"] = "6",
                    ["回数"] = "28"
                },
                new Dictionary<string, object>
                {
                    ["No"] = 2,
                    ["受給者番号"] = "2345678901",
                    ["事業者コード"] = "1310100002",
                    ["サービス提供年月"] = "202505",
                    ["サービスコード"] = "010102",
                    ["サービス名称"] = "福祉短期入所Ⅱ",
                    ["算定時間"] = "5",
                    ["回数"] = "25"
                },
                new Dictionary<string, object>
                {
                    ["No"] = 3,
                    ["受給者番号"] = "3456789012",
                    ["事業者コード"] = "1310100003",
                    ["サービス提供年月"] = "202505",
                    ["サービスコード"] = "010103",
                    ["サービス名称"] = "福祉短期入所Ⅲ",
                    ["算定時間"] = "5",
                    ["回数"] = "30"
                }
            };

            return NationalDataService.CreateFromDictionaries(sampleData);
        }

        /// <summary>
        /// 读取受给者服务信息数据（从SQLite数据库）
        /// </summary>
        private List<RecipientServiceInfo> LoadRecipientServiceData()
        {
            try
            {
                // 从数据库读取受给者数据
                var recipientData = Task.Run(async () => await LoadRecipientDataFromDatabase()).Result;

                // 转换为RecipientServiceInfo格式
                var result = ConvertRecipientToServiceInfo(recipientData);
                System.Diagnostics.Debug.WriteLine($"从数据库加载受给者数据: {result.Count}条");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"受给者服务信息数据读取失败: {ex.Message}，使用示例数据");
                return GetRecipientSampleData();
            }
        }

        /// <summary>
        /// 从数据库加载受给者数据
        /// </summary>
        private async Task<List<RecipientData>> LoadRecipientDataFromDatabase()
        {
            try
            {
                DateTime? selectedDate = null;

                // 在UI线程中获取选择的日期
                Dispatcher.Invoke(() =>
                {
                    selectedDate = RequestDatePicker.SelectedDate;
                });

                if (!selectedDate.HasValue)
                {
                    // 如果没有选择日期，返回所有数据
                    return await _recipientRepository.GetAllAsync();
                }

                // 根据选择的日期筛选数据
                var filterDate = selectedDate.Value;
                var serviceMonth = $"{filterDate.Year:D4}-{filterDate.Month:D2}";

                return await _recipientRepository.GetByServiceMonthAsync(serviceMonth);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从数据库加载受给者数据失败: {ex.Message}");
                return new List<RecipientData>();
            }
        }

        /// <summary>
        /// 将RecipientData转换为RecipientServiceInfo格式
        /// </summary>
        private List<RecipientServiceInfo> ConvertRecipientToServiceInfo(List<RecipientData> recipientData)
        {
            var result = new List<RecipientServiceInfo>();

            foreach (var item in recipientData)
            {
                var serviceInfo = new RecipientServiceInfo();
                serviceInfo["No"] = item.No;
                serviceInfo["受給者番号"] = item.受給者番号;
                serviceInfo["事業者番号"] = item.事業者番号;
                serviceInfo["サービス提供年月"] = item.サービス提供年月;
                serviceInfo["サービスコード"] = item.サービスコード;
                serviceInfo["サービス内容"] = item.サービス内容;
                serviceInfo["利用日数"] = item.利用日数;
                serviceInfo["status"] = item.status;

                result.Add(serviceInfo);
            }

            return result;
        }

        /// <summary>
        /// 获取受给者示例数据
        /// </summary>
        private List<RecipientServiceInfo> GetRecipientSampleData()
        {
            var sampleData = new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    ["No"] = 1,
                    ["受給者番号"] = "1234567890",
                    ["事業者番号"] = "1310100001",
                    ["サービス提供年月"] = "202505",
                    ["サービスコード"] = "010101",
                    ["サービス内容"] = "福祉短期入所Ⅰ６",
                    ["利用日数"] = "6"
                },
                new Dictionary<string, object>
                {
                    ["No"] = 2,
                    ["受給者番号"] = "2345678901",
                    ["事業者番号"] = "1310100002",
                    ["サービス提供年月"] = "202505",
                    ["サービスコード"] = "010102",
                    ["サービス内容"] = "福祉短期入所Ⅱ",
                    ["利用日数"] = "6"  // 故意设置不同的值来测试MISMATCH
                },
                new Dictionary<string, object>
                {
                    ["No"] = 4,  // 故意设置不存在的No来测试NO MATCH
                    ["受給者番号"] = "4567890123",
                    ["事業者番号"] = "1310100004",
                    ["サービス提供年月"] = "202505",
                    ["サービスコード"] = "010104",
                    ["サービス内容"] = "福祉短期入所Ⅳ",
                    ["利用日数"] = "8"
                }
            };

            return RecipientServiceInfoService.CreateFromDictionaries(sampleData);
        }

        /// <summary>
        /// 执行数据照合（只对未匹配或匹配失败的数据进行重新匹配）
        /// </summary>
        private List<ReconciliationResult> PerformDataReconciliation(List<NationalData> nationalData, List<RecipientServiceInfo> recipientData)
        {
            var results = new List<ReconciliationResult>();
            int currentNo = 1;

            System.Diagnostics.Debug.WriteLine($"开始数据照合 - 国保联数据: {nationalData.Count}条, 受给者数据: {recipientData.Count}条");

            foreach (var national in nationalData)
            {
                // 获取国保联数据的关键字段
                var nationalRecipientNumber = national["受給者番号"]?.ToString() ?? "";
                var nationalProviderCode = national["事業者コード"]?.ToString() ?? "";
                var nationalServiceYear = national["サービス提供年月"]?.ToString() ?? "";
                var nationalServiceCode = national["サービスコード"]?.ToString() ?? "";
                var nationalServiceName = national["サービス名称"]?.ToString() ?? "";
                var nationalCalculatedTime = national["算定時間"]?.ToString() ?? "";
                var nationalCount = national["回数"]?.ToString() ?? "";
                var nationalStatus = national["status"]?.ToString() ?? "";

                System.Diagnostics.Debug.WriteLine($"处理国保联数据 No: {currentNo}, 受給者番号: {nationalRecipientNumber}, 状态: {nationalStatus}");

                // 检查是否已经匹配成功，如果是则跳过重新匹配
                if (nationalStatus == "MATCH")
                {
                    System.Diagnostics.Debug.WriteLine($"跳过已匹配成功的记录: {nationalRecipientNumber}");

                    // 通过三字段联合匹配查找受给者数据（用于显示）
                    var existingMatchedRecipient = recipientData.FirstOrDefault(r =>
                        r["受給者番号"]?.ToString() == nationalRecipientNumber &&
                        r["事業者番号"]?.ToString() == nationalProviderCode &&
                        r["サービスコード"]?.ToString() == nationalServiceCode);

                    string recipientRecipientNumber = "";
                    string recipientProviderNumber = "";
                    string recipientServiceYear = "";
                    string recipientServiceCode = "";
                    string recipientServiceContent = "";
                    string recipientUsageDays = "";
                    string recipientUsageCount = "";

                    if (existingMatchedRecipient != null)
                    {
                        recipientRecipientNumber = existingMatchedRecipient["受給者番号"]?.ToString() ?? "";
                        recipientProviderNumber = existingMatchedRecipient["事業者番号"]?.ToString() ?? "";
                        recipientServiceYear = existingMatchedRecipient["サービス提供年月"]?.ToString() ?? "";
                        recipientServiceCode = existingMatchedRecipient["サービスコード"]?.ToString() ?? "";
                        recipientServiceContent = existingMatchedRecipient["サービス内容"]?.ToString() ?? "";
                        recipientUsageDays = existingMatchedRecipient["利用日数"]?.ToString() ?? "";
                        recipientUsageCount = existingMatchedRecipient["利用日数"]?.ToString() ?? "";
                    }

                    // 创建结果但保持原有状态
                    var existingResult = new ReconciliationResult
                    {
                        No = currentNo,
                        NationalRecipientNumber = nationalRecipientNumber,
                        NationalProviderCode = nationalProviderCode,
                        NationalServiceYear = nationalServiceYear,
                        NationalServiceCode = nationalServiceCode,
                        NationalServiceName = nationalServiceName,
                        NationalCalculatedTime = nationalCalculatedTime,
                        NationalCount = nationalCount,

                        RecipientRecipientNumber = recipientRecipientNumber,
                        RecipientProviderNumber = recipientProviderNumber,
                        RecipientServiceYear = recipientServiceYear,
                        RecipientServiceCode = recipientServiceCode,
                        RecipientServiceContent = recipientServiceContent,
                        RecipientUsageDays = recipientUsageDays,
                        RecipientUsageCount = recipientUsageCount,

                        OverallMatchStatus = "MATCH", // 保持已匹配状态
                        ProcessingTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    };

                    // 计算各字段的匹配状态
                    existingResult.RecipientNumberStatus = CompareFields(nationalRecipientNumber, recipientRecipientNumber);
                    existingResult.ProviderCodeStatus = CompareFields(nationalProviderCode, recipientProviderNumber);
                    existingResult.ServiceYearStatus = CompareFields(nationalServiceYear, recipientServiceYear);
                    existingResult.ServiceCodeStatus = CompareFields(nationalServiceCode, recipientServiceCode);
                    existingResult.ServiceNameStatus = CompareFields(nationalServiceName, recipientServiceContent);
                    existingResult.TimeStatus = CompareFields(nationalCalculatedTime, recipientUsageDays);
                    existingResult.CountStatus = CompareFields(nationalCount, recipientUsageCount);

                    results.Add(existingResult);
                    currentNo++;
                    continue;
                }

                // 对未匹配或匹配失败的数据进行重新匹配
                System.Diagnostics.Debug.WriteLine($"重新匹配数据: {nationalRecipientNumber}, 原状态: {nationalStatus}");

                // 通过受給者番号/事業者編号/サービスコード三个字段联合匹配受给者数据
                var matchedRecipient = recipientData.FirstOrDefault(r =>
                    r["受給者番号"]?.ToString() == nationalRecipientNumber &&
                    r["事業者番号"]?.ToString() == nationalProviderCode &&
                    r["サービスコード"]?.ToString() == nationalServiceCode);

                System.Diagnostics.Debug.WriteLine($"重新匹配结果: {(matchedRecipient != null ? "找到匹配" : "未找到匹配")}");

                string newRecipientRecipientNumber = "";
                string newRecipientProviderNumber = "";
                string newRecipientServiceYear = "";
                string newRecipientServiceCode = "";
                string newRecipientServiceContent = "";
                string newRecipientUsageDays = "";
                string newRecipientUsageCount = "";

                if (matchedRecipient != null)
                {
                    newRecipientRecipientNumber = matchedRecipient["受給者番号"]?.ToString() ?? "";
                    newRecipientProviderNumber = matchedRecipient["事業者番号"]?.ToString() ?? "";
                    newRecipientServiceYear = matchedRecipient["サービス提供年月"]?.ToString() ?? "";
                    newRecipientServiceCode = matchedRecipient["サービスコード"]?.ToString() ?? "";
                    newRecipientServiceContent = matchedRecipient["サービス内容"]?.ToString() ?? "";
                    newRecipientUsageDays = matchedRecipient["利用日数"]?.ToString() ?? "";
                    newRecipientUsageCount = matchedRecipient["利用日数"]?.ToString() ?? "";
                }

                // 执行字段对比
                var result = new ReconciliationResult
                {
                    No = currentNo,
                    NationalRecipientNumber = nationalRecipientNumber,
                    NationalProviderCode = nationalProviderCode,
                    NationalServiceYear = nationalServiceYear,
                    NationalServiceCode = nationalServiceCode,
                    NationalServiceName = nationalServiceName,
                    NationalCalculatedTime = nationalCalculatedTime,
                    NationalCount = nationalCount,

                    RecipientRecipientNumber = newRecipientRecipientNumber,
                    RecipientProviderNumber = newRecipientProviderNumber,
                    RecipientServiceYear = newRecipientServiceYear,
                    RecipientServiceCode = newRecipientServiceCode,
                    RecipientServiceContent = newRecipientServiceContent,
                    RecipientUsageDays = newRecipientUsageDays,
                    RecipientUsageCount = newRecipientUsageCount,

                    ProcessingTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                // 计算各字段的匹配状态
                result.RecipientNumberStatus = CompareFields(nationalRecipientNumber, newRecipientRecipientNumber);
                result.ProviderCodeStatus = CompareFields(nationalProviderCode, newRecipientProviderNumber);
                result.ServiceYearStatus = CompareFields(nationalServiceYear, newRecipientServiceYear);
                result.ServiceCodeStatus = CompareFields(nationalServiceCode, newRecipientServiceCode);
                result.ServiceNameStatus = CompareFields(nationalServiceName, newRecipientServiceContent);
                result.TimeStatus = CompareFields(nationalCalculatedTime, newRecipientUsageDays);
                result.CountStatus = CompareFields(nationalCount, newRecipientUsageCount);

                // 计算总体匹配状态
                if (matchedRecipient == null)
                {
                    result.OverallMatchStatus = "NOMATCH";
                }
                else
                {
                    bool isCompleteMatch = result.RecipientNumberStatus == "MATCH" &&
                                          result.ProviderCodeStatus == "MATCH" &&
                                          result.ServiceYearStatus == "MATCH" &&
                                          result.ServiceCodeStatus == "MATCH" &&
                                          result.ServiceNameStatus == "MATCH" &&
                                          result.TimeStatus == "MATCH" &&
                                          result.CountStatus == "MATCH";

                    result.OverallMatchStatus = isCompleteMatch ? "MATCH" : "MISMATCH";
                }

                results.Add(result);
                currentNo++;

                System.Diagnostics.Debug.WriteLine($"添加重新匹配结果 No: {currentNo - 1}, 新状态: {result.OverallMatchStatus}");
            }

            System.Diagnostics.Debug.WriteLine($"照合完成，共生成 {results.Count} 条结果");
            return results;
        }

        /// <summary>
        /// 比较两个字段的值
        /// </summary>
        private string CompareFields(string nationalValue, string recipientValue)
        {
            if (string.IsNullOrEmpty(nationalValue) && string.IsNullOrEmpty(recipientValue))
                return "MATCH";

            if (string.IsNullOrEmpty(nationalValue) || string.IsNullOrEmpty(recipientValue))
                return "NO MATCH";

            return nationalValue.Trim().Equals(recipientValue.Trim(), StringComparison.OrdinalIgnoreCase)
                ? "MATCH" : "MISMATCH";
        }

        /// <summary>
        /// 计算总体匹配状态
        /// </summary>
        private string CalculateOverallStatus(ReconciliationResult result)
        {
            var statuses = new[]
            {
                result.RecipientNumberStatus,
                result.ProviderCodeStatus,
                result.ServiceYearStatus,
                result.ServiceCodeStatus,
                result.ServiceNameStatus,
                result.TimeStatus,
                result.CountStatus
            };

            if (statuses.Any(s => s == "NO MATCH"))
                return "NO MATCH";

            if (statuses.Any(s => s == "MISMATCH"))
                return "MISMATCH";

            return "MATCH";
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            var totalRecords = _allResults?.Count ?? 0;
            var matchedRecords = _allResults?.Count(r => r.OverallMatchStatus == "MATCH") ?? 0;
            var mismatchedRecords = _allResults?.Count(r => r.OverallMatchStatus == "MISMATCH") ?? 0;
            var noMatchRecords = _allResults?.Count(r => r.OverallMatchStatus == "NOMATCH") ?? 0;

            System.Diagnostics.Debug.WriteLine($"统计信息 - 总数: {totalRecords}, MATCH: {matchedRecords}, MISMATCH: {mismatchedRecords}, NOMATCH: {noMatchRecords}");

            // 更新主窗口状态栏
            var mainWindow = Application.Current.MainWindow as MainWindow;
            var paginationControl = this.FindName("PaginationControl") as Controls.PaginationControl;
            var totalPages = paginationControl?.TotalPages ?? 1;
            mainWindow?.UpdateStatusBar(totalRecords, 0, totalPages,
                $"MATCH: {matchedRecords}, MISMATCH: {mismatchedRecords}, NOMATCH: {noMatchRecords}");
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            var totalItems = _allResults?.Count ?? 0;

            // 确保当前页面有效
            if (_currentPage < 1) _currentPage = 1;
            var totalPages = totalItems > 0 ? (int)Math.Ceiling((double)totalItems / _pageSize) : 1;
            if (_currentPage > totalPages) _currentPage = totalPages;

            var startIndex = (_currentPage - 1) * _pageSize;
            var pagedData = _allResults?.Skip(startIndex).Take(_pageSize).ToList() ?? new List<ReconciliationResult>();

            System.Diagnostics.Debug.WriteLine($"ApplyPagination - 总数据: {totalItems}, 当前页: {_currentPage}, 总页数: {totalPages}, 页面大小: {_pageSize}, 显示数据: {pagedData.Count}");

            ReconciliationResults.Clear();
            foreach (var item in pagedData)
            {
                ReconciliationResults.Add(item);
            }

            // 更新分页控件
            PaginationControl.TotalRecords = totalItems;
            PaginationControl.CurrentPage = _currentPage;
            PaginationControl.PageSize = _pageSize;
            PaginationControl.TotalPages = totalPages;

            // 确保分页事件绑定
            PaginationControl.PageChanged -= PaginationControl_PageChanged;
            PaginationControl.PageChanged += PaginationControl_PageChanged;

            System.Diagnostics.Debug.WriteLine($"分页更新完成 - 显示第 {_currentPage} 页，共 {totalPages} 页，显示 {pagedData.Count} 条记录");
        }

        /// <summary>
        /// 分页控件页面变化事件
        /// </summary>
       /* private void PaginationControl_PageChanged(object sender, PageChangedEventArgs e)
        {
            var paginationControl = sender as Controls.PaginationControl;
            if (paginationControl != null)
            {
                _currentPage = paginationControl.CurrentPage;
                _pageSize = paginationControl.PageSize;
                ApplyPagination();

                System.Diagnostics.Debug.WriteLine($"分页变化事件 - 新页面: {_currentPage}, 页大小: {_pageSize}");
            }
            _currentPage = newPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, PageSizeChangedEventArgs e)
        {
            _pageSize = newPageSize;
            _currentPage = 1;
            ApplyPagination();
        }*/

        /// <summary>
        /// 分页控件页码变化事件
        /// </summary>
        private void PaginationControl_PageChanged(object sender, PageChangedEventArgs e)
        {
            _currentPage = e.NewPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, PageSizeChangedEventArgs e)
        {
            _pageSize = e.NewPageSize;
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("エクスポート機能は実装中です。", "情報",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            UpdateStatistics();
            ApplyPagination();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// 应用筛选条件
        /// </summary>
        private void ApplyFilters()
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await LoadFilteredDataAsync();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"筛选数据加载失败: {ex.Message}");
                    Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show($"筛选数据加载失败: {ex.Message}", "错误",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });
        }

        /// <summary>
        /// 加载筛选后的数据
        /// </summary>
        private async Task LoadFilteredDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // 1. 读取国保连数据（应用时间筛选）
                    var nationalData = LoadNationalData();

                    // 2. 读取受给者服务信息数据
                    var recipientData = LoadRecipientServiceData();

                    // 3. 基于已有状态生成照合结果
                    var allResults = GenerateReconciliationFromExistingStatus(nationalData, recipientData);

                    // 4. 应用搜索和状态筛选
                    var filteredResults = ApplySearchAndStatusFilters(allResults);

                    _allResults = filteredResults;

                    // 5. 重置到第一页并应用分页显示
                    Dispatcher.Invoke(() =>
                    {
                        _currentPage = 1;
                        ApplyPagination();
                        UpdateStatistics();
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"筛选数据加载失败: {ex.Message}");
                    throw;
                }
            });
        }

        /// <summary>
        /// 应用搜索和状态筛选
        /// </summary>
        private List<ReconciliationResult> ApplySearchAndStatusFilters(List<ReconciliationResult> results)
        {
            var filteredResults = results;

            // 获取UI控件的值
            string searchText = "";
            string selectedStatus = "";

            Dispatcher.Invoke(() =>
            {
                searchText = NationalIdSearchBox.Text?.Trim() ?? "";
                var selectedItem = StatusFilterComboBox.SelectedItem as ComboBoxItem;
                selectedStatus = selectedItem?.Content?.ToString() ?? "";
            });

            System.Diagnostics.Debug.WriteLine($"应用筛选 - 搜索文本: '{searchText}', 状态筛选: '{selectedStatus}'");

            // 应用搜索文本筛选
            if (!string.IsNullOrEmpty(searchText) && searchText != "国保联ID・受給者ID・事業者コード")
            {
                var searchTextLower = searchText.ToLower();
                filteredResults = filteredResults.Where(r =>
                    (r.NationalRecipientNumber?.ToLower().Contains(searchTextLower) == true) ||
                    (r.RecipientRecipientNumber?.ToLower().Contains(searchTextLower) == true) ||
                    (r.NationalProviderCode?.ToLower().Contains(searchTextLower) == true) ||
                    (r.RecipientProviderNumber?.ToLower().Contains(searchTextLower) == true) ||
                    (r.NationalServiceCode?.ToLower().Contains(searchTextLower) == true) ||
                    (r.RecipientServiceCode?.ToLower().Contains(searchTextLower) == true) ||
                    (r.NationalServiceName?.ToLower().Contains(searchTextLower) == true) ||
                    (r.RecipientServiceContent?.ToLower().Contains(searchTextLower) == true)
                ).ToList();

                System.Diagnostics.Debug.WriteLine($"搜索文本筛选后: {filteredResults.Count} 条记录");
            }

            // 应用状态筛选
            if (!string.IsNullOrEmpty(selectedStatus) && selectedStatus != "すべての状態")
            {
                filteredResults = filteredResults.Where(r => r.OverallMatchStatus == selectedStatus).ToList();
                System.Diagnostics.Debug.WriteLine($"状态筛选后: {filteredResults.Count} 条记录");
            }

            System.Diagnostics.Debug.WriteLine($"最终筛选结果: {filteredResults.Count} 条记录");
            return filteredResults;
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 状态筛选变化时自动重新加载数据
            if (_allResults != null && _allResults.Count > 0)
            {
                ApplyFilters();
            }
        }

        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        private void NationalIdSearchBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null && textBox.Text == "国保联ID・受給者ID・事業者コード")
            {
                textBox.Text = "";
                textBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void NationalIdSearchBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null && string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "国保联ID・受給者ID・事業者コード";
                textBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        /// <summary>
        /// 搜索框文本变化事件（实时搜索）
        /// </summary>
        private void NationalIdSearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null &&
                textBox.Text != "国保联ID・受給者ID・事業者コード" &&
                _allResults != null && _allResults.Count > 0)
            {
                // 延迟执行搜索，避免频繁触发
                _searchTimer?.Stop();
                _searchTimer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(500) // 500ms延迟
                };
                _searchTimer.Tick += (s, args) =>
                {
                    _searchTimer.Stop();
                    ApplyFilters();
                };
                _searchTimer.Start();
            }
        }

        /// <summary>
        /// 时间筛选控件选择变化事件
        /// </summary>
        private void RequestDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            // 时间变化时自动重新加载数据
            ApplyFilters();
        }

        /// <summary>
        /// 更新匹配结果到数据库
        /// </summary>
        private void UpdateMatchResultsToDatabase(List<ReconciliationResult> results)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始更新匹配结果到数据库，共{results.Count}条结果");

                // 更新受给者数据状态
                int recipientUpdatedCount = 0;
                foreach (var result in results)
                {
                    try
                    {
                        // 通过受給者番号、事業者番号、サービスコード查找受给者数据记录
                        var recipientRecords = Task.Run(async () =>
                            await _recipientRepository.GetByConditionAsync(
                                "\"受給者番号\" = @recipientNumber AND \"事業者番号\" = @providerNumber AND \"サービスコード\" = @serviceCode",
                                _recipientRepository.CreateParameter("@recipientNumber", result.RecipientRecipientNumber),
                                _recipientRepository.CreateParameter("@providerNumber", result.RecipientProviderNumber),
                                _recipientRepository.CreateParameter("@serviceCode", result.RecipientServiceCode)
                            )).Result;

                        foreach (var recipient in recipientRecords)
                        {
                            // 更新status字段
                            Task.Run(async () => await _recipientRepository.UpdateStatusAsync(recipient.No, result.OverallMatchStatus)).Wait();
                            recipientUpdatedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"更新受给者数据状态失败: {ex.Message}");
                    }
                }

                // 更新国保联数据状态
                int nationalUpdatedCount = 0;
                foreach (var result in results)
                {
                    try
                    {
                        // 通过受給者番号、事業者コード、サービスコード查找国保联数据记录
                        var nationalRecords = Task.Run(async () =>
                            await _kokuhoRenRepository.GetByConditionAsync(
                                "受給者番号 = @recipientNumber AND 事業者コード = @providerCode AND サービスコード = @serviceCode",
                                _kokuhoRenRepository.CreateParameter("@recipientNumber", result.NationalRecipientNumber),
                                _kokuhoRenRepository.CreateParameter("@providerCode", result.NationalProviderCode),
                                _kokuhoRenRepository.CreateParameter("@serviceCode", result.NationalServiceCode)
                            )).Result;

                        foreach (var national in nationalRecords)
                        {
                            // 更新status字段
                            Task.Run(async () => await _kokuhoRenRepository.UpdateStatusAsync(national.No, result.OverallMatchStatus)).Wait();
                            nationalUpdatedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"更新国保联数据状态失败: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"成功更新受给者数据 {recipientUpdatedCount} 条记录，国保联数据 {nationalUpdatedCount} 条记录的匹配状态到数据库");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新匹配结果到数据库失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 详情按钮点击事件
        /// </summary>
        private void DetailButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.DataContext as ReconciliationResult;
            if (selectedItem == null) return;

            // 创建详情显示窗口
            var detailWindow = new Window
            {
                Title = $"照合詳細 - No.{selectedItem.No}",
                Width = 700,
                Height = 600,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = Window.GetWindow(this)
            };

            // 创建详情内容
            var detailContent = CreateDetailContent(selectedItem);
            detailWindow.Content = detailContent;

            detailWindow.ShowDialog();
        }

        /// <summary>
        /// 更新按钮点击事件
        /// </summary>
        private void UpdateButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.DataContext as ReconciliationResult;
            if (selectedItem == null) return;

            var result = MessageBox.Show(
                $"No.{selectedItem.No}のデータを更新しますか？",
                "更新確認",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // 这里可以添加更新逻辑
                MessageBox.Show("データが更新されました。", "更新完了",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 创建详情显示内容
        /// </summary>
        private ScrollViewer CreateDetailContent(ReconciliationResult item)
        {
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(20)
            };

            var stackPanel = new StackPanel();

            // 添加详情信息
            AddDetailSection(stackPanel, "基本情報", new[]
            {
                ("No", item.No.ToString()),
                ("照合状態", item.OverallMatchStatus),
                ("処理時間", item.ProcessingTime)
            });

            AddDetailSection(stackPanel, "受給者番号", new[]
            {
                ("国保連", item.NationalRecipientNumber),
                ("受給者", item.RecipientRecipientNumber),
                ("状態", item.RecipientNumberStatus)
            });

            AddDetailSection(stackPanel, "事業者コード", new[]
            {
                ("国保連", item.NationalProviderCode),
                ("受給者", item.RecipientProviderNumber),
                ("状態", item.ProviderCodeStatus)
            });

            AddDetailSection(stackPanel, "サービス提供年月", new[]
            {
                ("国保連", item.NationalServiceYear),
                ("受給者", item.RecipientServiceYear),
                ("状態", item.ServiceYearStatus)
            });

            AddDetailSection(stackPanel, "サービスコード", new[]
            {
                ("国保連", item.NationalServiceCode),
                ("受給者", item.RecipientServiceCode),
                ("状態", item.ServiceCodeStatus)
            });

            AddDetailSection(stackPanel, "サービス名称/内容", new[]
            {
                ("国保連", item.NationalServiceName),
                ("受給者", item.RecipientServiceContent),
                ("状態", item.ServiceNameStatus)
            });

            AddDetailSection(stackPanel, "算定時間/利用日数", new[]
            {
                ("国保連", item.NationalCalculatedTime),
                ("受給者", item.RecipientUsageDays),
                ("状態", item.TimeStatus)
            });

            AddDetailSection(stackPanel, "回数/利用日数", new[]
            {
                ("国保連", item.NationalCount),
                ("受給者", item.RecipientUsageCount),
                ("状態", item.CountStatus)
            });

            scrollViewer.Content = stackPanel;
            return scrollViewer;
        }

        /// <summary>
        /// 添加详情区段
        /// </summary>
        private void AddDetailSection(StackPanel parent, string title, (string label, string value)[] items)
        {
            // 标题
            var titleBlock = new TextBlock
            {
                Text = title,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 15, 0, 10),
                Foreground = Brushes.DarkSlateGray
            };
            parent.Children.Add(titleBlock);

            // 内容网格
            var grid = new Grid
            {
                Margin = new Thickness(10, 0, 0, 0)
            };

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            for (int i = 0; i < items.Length; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                var labelBlock = new TextBlock
                {
                    Text = items[i].label + ":",
                    FontWeight = FontWeights.Medium,
                    Margin = new Thickness(0, 2, 10, 2),
                    VerticalAlignment = VerticalAlignment.Top
                };
                Grid.SetRow(labelBlock, i);
                Grid.SetColumn(labelBlock, 0);
                grid.Children.Add(labelBlock);

                var valueBlock = new TextBlock
                {
                    Text = items[i].value ?? "-",
                    Margin = new Thickness(0, 2, 0, 2),
                    TextWrapping = TextWrapping.Wrap,
                    VerticalAlignment = VerticalAlignment.Top
                };
                Grid.SetRow(valueBlock, i);
                Grid.SetColumn(valueBlock, 1);
                grid.Children.Add(valueBlock);
            }

            parent.Children.Add(grid);
        }
    }

    /// <summary>
    /// 照合结果数据模型
    /// </summary>
    public class ReconciliationResult
    {
        public int No { get; set; }
        public int KokuhoRenDataNo { get; set; }

        public int RecipientsDataNo { get; set; }

        // 国保联数据字段
        public string NationalRecipientNumber { get; set; }
        public string NationalProviderCode { get; set; }
        public string NationalServiceYear { get; set; }
        public string NationalServiceCode { get; set; }
        public string NationalServiceName { get; set; }
        public string NationalCalculatedTime { get; set; }
        public string NationalCount { get; set; }

        // 受给者数据字段
        public string RecipientRecipientNumber { get; set; }
        public string RecipientProviderNumber { get; set; }
        public string RecipientServiceYear { get; set; }
        public string RecipientServiceCode { get; set; }
        public string RecipientServiceContent { get; set; }
        public string RecipientUsageDays { get; set; }
        public string RecipientUsageCount { get; set; }

        // 对比状态字段
        public string RecipientNumberStatus { get; set; }
        public string ProviderCodeStatus { get; set; }
        public string ServiceYearStatus { get; set; }
        public string ServiceCodeStatus { get; set; }
        public string ServiceNameStatus { get; set; }
        public string TimeStatus { get; set; }
        public string CountStatus { get; set; }

        public string OverallMatchStatus { get; set; }
        public string ProcessingTime { get; set; }
    }
}
