using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace NAVI.Controls
{
    /// <summary>
    /// 年月选择器控件
    /// </summary>
    public partial class MonthYearPicker : UserControl, INotifyPropertyChanged
    {
        #region 依赖属性

        /// <summary>
        /// 选中的日期
        /// </summary>
        public static readonly DependencyProperty SelectedDateProperty =
            DependencyProperty.Register("SelectedDate", typeof(DateTime?), typeof(MonthYearPicker),
                new PropertyMetadata(null, OnSelectedDateChanged));

        /// <summary>
        /// 显示文本
        /// </summary>
        public static readonly DependencyProperty DisplayTextProperty =
            DependencyProperty.Register("DisplayText", typeof(string), typeof(MonthYearPicker),
                new PropertyMetadata("年月を選択"));

        /// <summary>
        /// 当前年份
        /// </summary>
        public static readonly DependencyProperty CurrentYearProperty =
            DependencyProperty.Register("CurrentYear", typeof(int), typeof(MonthYearPicker),
                new PropertyMetadata(DateTime.Now.Year));

        #endregion

        #region 属性

        /// <summary>
        /// 选中的日期
        /// </summary>
        public DateTime? SelectedDate
        {
            get => (DateTime?)GetValue(SelectedDateProperty);
            set => SetValue(SelectedDateProperty, value);
        }

        /// <summary>
        /// 显示文本
        /// </summary>
        public string DisplayText
        {
            get => (string)GetValue(DisplayTextProperty);
            set => SetValue(DisplayTextProperty, value);
        }

        /// <summary>
        /// 当前年份
        /// </summary>
        public int CurrentYear
        {
            get => (int)GetValue(CurrentYearProperty);
            set => SetValue(CurrentYearProperty, value);
        }

        /// <summary>
        /// 临时选择的年份
        /// </summary>
        private int _tempYear;

        /// <summary>
        /// 临时选择的月份
        /// </summary>
        private int _tempMonth;

        #endregion

        #region 事件

        /// <summary>
        /// 选择日期改变事件
        /// </summary>
        public event EventHandler<DateTime?> SelectedDateChanged;

        /// <summary>
        /// 属性改变事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        #endregion

        #region 构造函数

        public MonthYearPicker()
        {
            InitializeComponent();
            DataContext = this;
            
            // 初始化临时值
            var now = DateTime.Now;
            _tempYear = now.Year;
            _tempMonth = now.Month;
            CurrentYear = now.Year;

            // 设置事件处理
            MainBorder.MouseLeftButtonDown += MainBorder_MouseLeftButtonDown;
            SelectionPopup.Opened += SelectionPopup_Opened;
            SelectionPopup.Closed += SelectionPopup_Closed;

            // 点击外部关闭弹窗
            this.Loaded += (s, e) =>
            {
                var window = Window.GetWindow(this);
                if (window != null)
                {
                    window.PreviewMouseLeftButtonDown += Window_PreviewMouseLeftButtonDown;
                }
            };
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 选择日期改变时的处理
        /// </summary>
        private static void OnSelectedDateChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is MonthYearPicker picker)
            {
                picker.UpdateDisplayText();
                picker.SelectedDateChanged?.Invoke(picker, (DateTime?)e.NewValue);
            }
        }

        /// <summary>
        /// 主边框点击事件
        /// </summary>
        private void MainBorder_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            SelectionPopup.IsOpen = !SelectionPopup.IsOpen;
        }

        /// <summary>
        /// 弹窗打开事件
        /// </summary>
        private void SelectionPopup_Opened(object sender, EventArgs e)
        {
            // 设置临时值为当前选择的值
            if (SelectedDate.HasValue)
            {
                _tempYear = SelectedDate.Value.Year;
                _tempMonth = SelectedDate.Value.Month;
                CurrentYear = _tempYear;
            }
            else
            {
                var now = DateTime.Now;
                _tempYear = now.Year;
                _tempMonth = now.Month;
                CurrentYear = _tempYear;
            }

            UpdateMonthButtonStyles();
        }

        /// <summary>
        /// 弹窗关闭事件
        /// </summary>
        private void SelectionPopup_Closed(object sender, EventArgs e)
        {
            // 弹窗关闭时重置月份按钮样式
            ResetMonthButtonStyles();
        }

        /// <summary>
        /// 窗口预览鼠标点击事件（用于点击外部关闭弹窗）
        /// </summary>
        private void Window_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (SelectionPopup.IsOpen)
            {
                // 检查点击是否在弹窗内部
                var popupChild = SelectionPopup.Child as FrameworkElement;
                if (popupChild != null)
                {
                    var position = e.GetPosition(popupChild);
                    var bounds = new Rect(0, 0, popupChild.ActualWidth, popupChild.ActualHeight);

                    if (!bounds.Contains(position))
                    {
                        SelectionPopup.IsOpen = false;
                    }
                }
            }
        }

        /// <summary>
        /// 上一年按钮点击
        /// </summary>
        private void PrevYearButton_Click(object sender, RoutedEventArgs e)
        {
            _tempYear--;
            CurrentYear = _tempYear;
        }

        /// <summary>
        /// 下一年按钮点击
        /// </summary>
        private void NextYearButton_Click(object sender, RoutedEventArgs e)
        {
            _tempYear++;
            CurrentYear = _tempYear;
        }

        /// <summary>
        /// 月份按钮点击
        /// </summary>
        private void MonthButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && int.TryParse(button.Tag.ToString(), out int month))
            {
                _tempMonth = month;
                UpdateMonthButtonStyles();
            }
        }

        /// <summary>
        /// 取消按钮点击
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            SelectionPopup.IsOpen = false;
        }

        /// <summary>
        /// 确定按钮点击
        /// </summary>
        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            SelectedDate = new DateTime(_tempYear, _tempMonth, 1);
            SelectionPopup.IsOpen = false;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新显示文本
        /// </summary>
        private void UpdateDisplayText()
        {
            if (SelectedDate.HasValue)
            {
                DisplayText = SelectedDate.Value.ToString("yyyy年MM月");
            }
            else
            {
                DisplayText = "年月を選択";
            }
        }

        /// <summary>
        /// 更新月份按钮样式
        /// </summary>
        private void UpdateMonthButtonStyles()
        {
            // 重置所有按钮样式
            ResetMonthButtonStyles();

            // 高亮选中的月份
            foreach (Button button in FindVisualChildren<Button>(SelectionPopup.Child))
            {
                if (int.TryParse(button.Tag?.ToString(), out int month) && month == _tempMonth)
                {
                    button.Background = new SolidColorBrush(Color.FromRgb(33, 150, 243));
                    button.Foreground = Brushes.White;
                    break;
                }
            }
        }

        /// <summary>
        /// 重置月份按钮样式
        /// </summary>
        private void ResetMonthButtonStyles()
        {
            foreach (Button button in FindVisualChildren<Button>(SelectionPopup.Child))
            {
                if (button.Tag != null && int.TryParse(button.Tag.ToString(), out _))
                {
                    button.ClearValue(Button.BackgroundProperty);
                    button.ClearValue(Button.ForegroundProperty);
                }
            }
        }

        /// <summary>
        /// 查找可视化树中的子元素
        /// </summary>
        private static System.Collections.Generic.IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        /// <summary>
        /// 触发属性改变事件
        /// </summary>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取格式化的年月字符串 (yyyyMM)
        /// </summary>
        public string GetFormattedYearMonth()
        {
            return SelectedDate?.ToString("yyyyMM") ?? "";
        }

        /// <summary>
        /// 设置年月值
        /// </summary>
        /// <param name="yearMonth">yyyyMM格式的字符串</param>
        public void SetYearMonth(string yearMonth)
        {
            if (string.IsNullOrEmpty(yearMonth) || yearMonth.Length != 6)
            {
                SelectedDate = null;
                return;
            }

            if (int.TryParse(yearMonth.Substring(0, 4), out int year) &&
                int.TryParse(yearMonth.Substring(4, 2), out int month) &&
                year >= 1900 && year <= 2100 && month >= 1 && month <= 12)
            {
                SelectedDate = new DateTime(year, month, 1);
            }
        }

        #endregion
    }
}
